{"expo": {"name": "Tirak", "slug": "tirak-companion-marketplace", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/images/splash-icon.png", "scheme": "tirak", "userInterfaceStyle": "automatic", "newArchEnabled": false, "splash": {"image": "./assets/images/splash-icon.png", "resizeMode": "contain", "backgroundColor": "#ffffff"}, "ios": {"supportsTablet": true, "bundleIdentifier": "com.tirak.pineapple", "infoPlist": {"ITSAppUsesNonExemptEncryption": false}, "icon": {"light": "./assets/images/splash-icon.png", "dark": "./assets/images/splash-icon.png", "tinted": "./assets/images/splash-icon.png"}}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/images/splash-icon.png", "backgroundColor": "#ffffff"}, "package": "com.tirak.pineapple", "enableProguardInReleaseBuilds": false, "enableHermes": true}, "web": {"favicon": "./assets/images/favicon.png"}, "plugins": [["expo-router", {"asyncRoutes": false}], ["@sentry/react-native/expo", {"url": "https://sentry.io/", "project": "react-native", "organization": "tirak"}]], "experiments": {"typedRoutes": true, "tsconfigPaths": true}, "extra": {"router": {}, "eas": {"projectId": "ba0d6f3e-ed4c-4aaf-8cff-aa2fdb253cc5"}}}}