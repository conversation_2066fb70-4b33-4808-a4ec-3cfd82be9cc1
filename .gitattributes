# Git attributes for consistent line endings and file handling

# Set default behavior to automatically normalize line endings
* text=auto

# Explicitly declare text files you want to always be normalized and converted
# to native line endings on checkout
*.js text
*.jsx text
*.ts text
*.tsx text
*.json text
*.md text
*.txt text
*.yml text
*.yaml text
*.xml text
*.html text
*.css text
*.scss text
*.sass text

# Declare files that will always have CRLF line endings on checkout
*.bat text eol=crlf

# Declare files that will always have LF line endings on checkout
*.sh text eol=lf

# Denote all files that are truly binary and should not be modified
*.png binary
*.jpg binary
*.jpeg binary
*.gif binary
*.ico binary
*.mov binary
*.mp4 binary
*.mp3 binary
*.flv binary
*.fla binary
*.swf binary
*.gz binary
*.zip binary
*.7z binary
*.ttf binary
*.eot binary
*.woff binary
*.woff2 binary
*.pyc binary
*.pdf binary
*.ez binary
*.bz2 binary
*.swp binary
*.jks binary
*.p8 binary
*.p12 binary
*.key binary
*.mobileprovision binary

# React Native specific
*.bundle binary
*.ipa binary
*.apk binary
*.aab binary

# Expo specific
*.exp binary
