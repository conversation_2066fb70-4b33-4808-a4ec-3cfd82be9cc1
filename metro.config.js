// Learn more https://docs.expo.io/guides/customizing-metro
const { getDefaultConfig } = require('expo/metro-config');

/** @type {import('expo/metro-config').MetroConfig} */
const config = getDefaultConfig(__dirname, {
  // Enable CSS support.
  isCSSEnabled: true,
});

// Ensure all necessary extensions are included
config.resolver.sourceExts = [
  ...config.resolver.sourceExts,
  'mjs',
  'cjs',
];

// Add platform-specific configurations for better Android APK support
config.resolver.platforms = ['native', 'android', 'ios', 'web'];

// Ensure proper asset resolution for production builds
config.resolver.assetExts = [
  ...config.resolver.assetExts,
  'bin',
  'txt',
  'jpg',
  'png',
  'json',
];

// Add transformer configuration for better route bundling
config.transformer = {
  ...config.transformer,
  minifierConfig: {
    // Preserve route names in production builds
    keep_fnames: true,
  },
};

module.exports = config;
