// Learn more https://docs.expo.io/guides/customizing-metro
const { getDefaultConfig } = require('expo/metro-config');

/** @type {import('expo/metro-config').MetroConfig} */
const config = getDefaultConfig(__dirname, {
  // Enable CSS support.
  isCSSEnabled: true,
});

// Add custom resolver for routes
config.resolver.resolveRequest = (context, moduleName, platform) => {
  // Handle route resolution
  if (moduleName.startsWith('/supplier/')) {
    return {
      filePath: moduleName.replace('/supplier/', './app/supplier/'),
      type: 'sourceFile',
    };
  }
  return context.resolveRequest(context, moduleName, platform);
};

// Ensure all necessary extensions are included
config.resolver.sourceExts = [
  ...config.resolver.sourceExts,
  'mjs',
  'cjs',
];

module.exports = config;
