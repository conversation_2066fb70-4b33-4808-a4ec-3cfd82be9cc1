// Learn more https://docs.expo.io/guides/customizing-metro
const { getDefaultConfig } = require('expo/metro-config');
const os = require('os');

// Polyfill for Node.js < 19
if (!os.availableParallelism) {
  os.availableParallelism = () => os.cpus().length;
}

/** @type {import('expo/metro-config').MetroConfig} */
const config = getDefaultConfig(__dirname);

// Optimize for ViewManager stability and prevent crashes
config.resolver.platforms = ['ios', 'android', 'native', 'web'];

// Add resolver configuration to handle ViewManager issues
config.resolver.resolverMainFields = ['react-native', 'browser', 'main'];

// Optimize transformer for better ViewManager compatibility
config.transformer.minifierConfig = {
  // Preserve ViewManager references during minification
  keep_fnames: true,
  mangle: {
    keep_fnames: true,
  },
};

// Add serializer configuration for better Android compatibility
config.serializer.createModuleIdFactory = function() {
  const fileToIdMap = new Map();
  let nextId = 0;
  return (path) => {
    if (!fileToIdMap.has(path)) {
      fileToIdMap.set(path, nextId++);
    }
    return fileToIdMap.get(path);
  };
};

module.exports = config;
