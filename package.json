{"name": "expo-app", "main": "expo-router/entry", "version": "1.0.0", "scripts": {"start": "expo start", "start-web": "expo start --web --tunnel", "start-web-dev": "DEBUG=expo* expo start --web --tunnel", "test": "jest"}, "dependencies": {"@expo/vector-icons": "^14.1.0", "@react-native-async-storage/async-storage": "2.1.2", "@react-native-community/datetimepicker": "8.4.1", "@react-navigation/native": "^7.1.6", "@sentry/react-native": "~6.14.0", "@tanstack/react-query": "^5.81.2", "axios": "^1.10.0", "expo": "^53.0.4", "expo-blur": "~14.1.4", "expo-constants": "~17.1.4", "expo-dev-client": "~5.2.4", "expo-font": "~13.3.0", "expo-haptics": "~14.1.4", "expo-image": "~2.3.2", "expo-image-picker": "~16.1.4", "expo-linear-gradient": "~14.1.5", "expo-linking": "~7.1.4", "expo-location": "~18.1.4", "expo-router": "~5.1.3", "expo-secure-store": "^14.2.3", "expo-splash-screen": "~0.30.7", "expo-status-bar": "~2.2.3", "expo-symbols": "~0.4.4", "expo-system-ui": "~5.0.6", "expo-web-browser": "~14.2.0", "lucide-react-native": "^0.475.0", "nativewind": "^4.1.23", "react": "19.0.0", "react-dom": "19.0.0", "react-native": "0.79.5", "react-native-gesture-handler": "~2.24.0", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.11.1", "react-native-svg": "15.11.2", "react-native-web": "^0.20.0", "zod": "^3.25.67", "zustand": "^5.0.5"}, "devDependencies": {"@babel/core": "^7.25.2", "@expo/ngrok": "^4.1.0", "@testing-library/react-native": "^13.2.0", "@types/react": "~19.0.10", "react-native-testing-library": "^6.0.0", "react-test-renderer": "19.0.0", "typescript": "~5.8.3"}, "private": true}