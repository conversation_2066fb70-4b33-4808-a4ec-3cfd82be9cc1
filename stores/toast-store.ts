import { create } from 'zustand';

interface Toast {
  message: string;
  type: 'success' | 'error' | 'warning' | 'info';
  duration?: number;
}

interface ToastStore {
  toast: Toast | null;
  showToast: (toast: Toast) => void;
  hideToast: () => void;
}

export const useToastStore = create<ToastStore>((set) => ({
  toast: null,
  showToast: (toast: Toast) => {
    set({ toast });
    // Auto-hide after duration
    setTimeout(() => {
      set({ toast: null });
    }, toast.duration || 3000);
  },
  hideToast: () => set({ toast: null }),
}));

// Helper functions for common toast types
export const useToast = () => {
  const { showToast } = useToastStore();
  
  return {
    success: (message: string, duration?: number) => 
      showToast({ type: 'success', message, duration }),
    
    error: (message: string, duration?: number) => 
      showToast({ type: 'error', message, duration }),
    
    warning: (message: string, duration?: number) => 
      showToast({ type: 'warning', message, duration }),
    
    info: (message: string, duration?: number) => 
      showToast({ type: 'info', message, duration }),
  };
};