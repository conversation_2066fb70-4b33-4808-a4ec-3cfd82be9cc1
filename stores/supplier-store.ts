import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { SupplierProfile, SupplierSignupData, SupplierStats } from '@/types/supplier';
import { mockSupplierProfile, mockSupplierStats } from '@/mocks/supplier-data';

interface SupplierState {
  isSupplier: boolean;
  profile: SupplierProfile | null;
  stats: SupplierStats | null;
  signupData: SupplierSignupData;
  isLoading: boolean;
  error: string | null;
  
  // Actions
  setIsSupplier: (isSupplier: boolean) => void;
  setProfile: (profile: SupplierProfile | null) => void;
  setStats: (stats: SupplierStats | null) => void;
  updateSignupData: (data: Partial<SupplierSignupData>) => void;
  resetSignupData: () => void;
  nextSignupStep: () => void;
  prevSignupStep: () => void;
  
  // API actions (mock)
  fetchProfile: () => Promise<void>;
  fetchStats: () => Promise<void>;
  submitSignup: () => Promise<boolean>;
  updateProfile: (data: Partial<SupplierProfile>) => Promise<boolean>;
  addService: (service: Omit<SupplierProfile['services'][0], 'id'>) => Promise<boolean>;
  updateService: (serviceId: string, data: Partial<SupplierProfile['services'][0]>) => Promise<boolean>;
  deleteService: (serviceId: string) => Promise<boolean>;
  updateAvailability: (availability: SupplierProfile['availability']) => Promise<boolean>;
}

const initialSignupData: SupplierSignupData = {
  step: 1,
  basicInfo: {
    firstName: '',
    lastName: '',
    displayName: '',
    phone: '',
    email: '',
    bio: '',
  },
  idVerification: {
    idCardFront: null,
    idCardBack: null,
    selfieWithId: null,
  },
  photos: [],
  categories: [],
  services: [],
  regions: [],
  availability: {
    weeklySchedule: {
      monday: [],
      tuesday: [],
      wednesday: [],
      thursday: [],
      friday: [],
      saturday: [],
      sunday: [],
    },
    exceptions: [],
  },
  subscription: {
    plan: 'basic',
    paymentMethod: 'promptpay',
    paymentComplete: false,
  },
};

export const useSupplierStore = create<SupplierState>()(
  persist(
    (set, get) => ({
      isSupplier: false,
      profile: null,
      stats: null,
      signupData: initialSignupData,
      isLoading: false,
      error: null,
      
      setIsSupplier: (isSupplier) => set({ isSupplier }),
      setProfile: (profile) => set({ profile }),
      setStats: (stats) => set({ stats }),
      
      updateSignupData: (data) => set((state) => ({
        signupData: { ...state.signupData, ...data }
      })),
      
      resetSignupData: () => set({ signupData: initialSignupData }),
      
      nextSignupStep: () => set((state) => ({
        signupData: {
          ...state.signupData,
          step: Math.min(state.signupData.step + 1, 8)
        }
      })),
      
      prevSignupStep: () => set((state) => ({
        signupData: {
          ...state.signupData,
          step: Math.max(state.signupData.step - 1, 1)
        }
      })),
      
      fetchProfile: async () => {
        set({ isLoading: true, error: null });
        try {
          // Simulate API call
          await new Promise(resolve => setTimeout(resolve, 1000));
          set({ profile: mockSupplierProfile, isLoading: false });
        } catch (error) {
          set({ error: 'Failed to fetch profile', isLoading: false });
        }
      },
      
      fetchStats: async () => {
        set({ isLoading: true, error: null });
        try {
          // Simulate API call
          await new Promise(resolve => setTimeout(resolve, 1000));
          set({ stats: mockSupplierStats, isLoading: false });
        } catch (error) {
          set({ error: 'Failed to fetch stats', isLoading: false });
        }
      },
      
      submitSignup: async () => {
        set({ isLoading: true, error: null });
        try {
          // Simulate API call
          await new Promise(resolve => setTimeout(resolve, 2000));
          set({ 
            isSupplier: true,
            profile: {
              ...mockSupplierProfile,
              displayName: get().signupData.basicInfo.displayName || mockSupplierProfile.displayName,
              bio: get().signupData.basicInfo.bio || mockSupplierProfile.bio,
              categories: get().signupData.categories.length ? get().signupData.categories : mockSupplierProfile.categories,
              services: get().signupData.services.length ? get().signupData.services : mockSupplierProfile.services,
              regions: get().signupData.regions.length ? get().signupData.regions : mockSupplierProfile.regions,
            },
            stats: mockSupplierStats,
            isLoading: false,
            signupData: initialSignupData
          });
          return true;
        } catch (error) {
          set({ error: 'Failed to submit signup', isLoading: false });
          return false;
        }
      },
      
      updateProfile: async (data) => {
        set({ isLoading: true, error: null });
        try {
          // Simulate API call
          await new Promise(resolve => setTimeout(resolve, 1000));
          set((state) => ({
            profile: state.profile ? { ...state.profile, ...data } : null,
            isLoading: false
          }));
          return true;
        } catch (error) {
          set({ error: 'Failed to update profile', isLoading: false });
          return false;
        }
      },
      
      addService: async (service) => {
        set({ isLoading: true, error: null });
        try {
          // Simulate API call
          await new Promise(resolve => setTimeout(resolve, 1000));
          const newService = {
            ...service,
            id: `serv-${Date.now()}`,
          };
          set((state) => ({
            profile: state.profile 
              ? { 
                  ...state.profile, 
                  services: [...state.profile.services, newService] 
                } 
              : null,
            isLoading: false
          }));
          return true;
        } catch (error) {
          set({ error: 'Failed to add service', isLoading: false });
          return false;
        }
      },
      
      updateService: async (serviceId, data) => {
        set({ isLoading: true, error: null });
        try {
          // Simulate API call
          await new Promise(resolve => setTimeout(resolve, 1000));
          set((state) => {
            if (!state.profile) return { isLoading: false };
            
            const updatedServices = state.profile.services.map(service => 
              service.id === serviceId ? { ...service, ...data } : service
            );
            
            return {
              profile: { ...state.profile, services: updatedServices },
              isLoading: false
            };
          });
          return true;
        } catch (error) {
          set({ error: 'Failed to update service', isLoading: false });
          return false;
        }
      },
      
      deleteService: async (serviceId) => {
        set({ isLoading: true, error: null });
        try {
          // Simulate API call
          await new Promise(resolve => setTimeout(resolve, 1000));
          set((state) => {
            if (!state.profile) return { isLoading: false };
            
            const updatedServices = state.profile.services.filter(
              service => service.id !== serviceId
            );
            
            return {
              profile: { ...state.profile, services: updatedServices },
              isLoading: false
            };
          });
          return true;
        } catch (error) {
          set({ error: 'Failed to delete service', isLoading: false });
          return false;
        }
      },
      
      updateAvailability: async (availability) => {
        set({ isLoading: true, error: null });
        try {
          // Simulate API call
          await new Promise(resolve => setTimeout(resolve, 1000));
          set((state) => ({
            profile: state.profile 
              ? { ...state.profile, availability } 
              : null,
            isLoading: false
          }));
          return true;
        } catch (error) {
          set({ error: 'Failed to update availability', isLoading: false });
          return false;
        }
      },
    }),
    {
      name: 'supplier-storage',
      storage: createJSONStorage(() => AsyncStorage),
      partialize: (state) => ({
        isSupplier: state.isSupplier,
        profile: state.profile,
      }),
    }
  )
);