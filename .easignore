# EAS Build ignore file
# Files and directories to exclude from EAS Build uploads

# Development files
.expo/
.vscode/
.cursor/

# Node modules (handled by package.json)
node_modules/

# Build artifacts
dist/
build/
web-build/

# Environment files
.env.local
.env.*.local

# OS files
.DS_Store
Thumbs.db

# IDE files
*.swp
*.swo
*~

# Logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/

# Dependency directories
jspm_packages/

# Optional npm cache directory
.npm

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# Metro cache
.metro-health-check*

# Temporary folders
tmp/
temp/
