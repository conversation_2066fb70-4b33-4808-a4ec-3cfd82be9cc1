{
    "event_id": "b37e9079266148c0b01f6b7b8a254c11",
    "project": 4509643525783552,
    "release": "com.tirak.pineapple@1.0.0+1",
    "dist": "1",
    "platform": "java",
    "message": "",
    "datetime": "2025-07-10T17:27:55+00:00",
    "tags": [
      [
        "device",
        "sdk_gphone64_arm64"
      ],
      [
        "device.class",
        "1"
      ],
      [
        "device.family",
        "sdk_gphone64_arm64"
      ],
      [
        "environment",
        "production"
      ],
      [
        "event.environment",
        "java"
      ],
      [
        "event.origin",
        "android"
      ],
      [
        "isSideLoaded",
        "true"
      ],
      [
        "level",
        "fatal"
      ],
      [
        "os",
        "Android 15"
      ],
      [
        "os.name",
        "Android"
      ],
      [
        "os.rooted",
        "no"
      ],
      [
        "dist",
        "1"
      ],
      [
        "release",
        "com.tirak.pineapple@1.0.0+1"
      ],
      [
        "user",
        "id:eae27f58-2d79-4ffe-86cf-01859a7cfdde"
      ]
    ],
    "_dsc": {
      "environment": "production",
      "public_key": "aa0f61b9f2d781f2a2295677370e6749",
      "release": "com.tirak.pineapple@1.0.0+1",
      "replay_id": "61c1a9cf-13d8-4c87-a240-b8e328c293cd",
      "trace_id": "1880224a3cdc4524b4c5f73a1c04e642",
      "transaction": null
    },
    "_metrics": {
      "bytes.ingested.event": 16392,
      "bytes.stored.event": 55681
    },
    "breadcrumbs": {
      "values": [
        {
          "timestamp": 1752168471.651,
          "type": "navigation",
          "category": "app.lifecycle",
          "level": "info",
          "data": {
            "state": "foreground"
          }
        },
        {
          "timestamp": 1752168472.192,
          "type": "system",
          "category": "network.event",
          "level": "info",
          "data": {
            "action": "NETWORK_AVAILABLE"
          }
        },
        {
          "timestamp": 1752168472.192,
          "type": "system",
          "category": "network.event",
          "level": "info",
          "data": {
            "action": "NETWORK_CAPABILITIES_CHANGED",
            "download_bandwidth": 30000,
            "network_type": "wifi",
            "signal_strength": -50,
            "upload_bandwidth": 12000,
            "vpn_active": false
          }
        },
        {
          "timestamp": 1752168473.768,
          "type": "http",
          "category": "xhr",
          "level": "info",
          "data": {
            "end_timestamp": 1752168473766.48,
            "method": "GET",
            "response_body_size": 1783,
            "start_timestamp": 1752168471906.59,
            "status_code": 200,
            "url": "https://tirak-backend.tirak-court.workers.dev/api/bookings?limit=5&page=1"
          },
          "origin": "react-native"
        },
        {
          "timestamp": 1752168473.77,
          "type": "http",
          "category": "xhr",
          "level": "info",
          "data": {
            "end_timestamp": 1752168473769.75,
            "method": "GET",
            "response_body_size": 11305,
            "start_timestamp": 1752168471904.31,
            "status_code": 200,
            "url": "https://tirak-backend.tirak-court.workers.dev/api/companions/all?sortBy=rating&sortOrder=desc"
          },
          "origin": "react-native"
        },
        {
          "timestamp": 1752168473.771,
          "type": "http",
          "category": "xhr",
          "level": "info",
          "data": {
            "end_timestamp": 1752168473770.69,
            "method": "GET",
            "response_body_size": 170,
            "start_timestamp": 1752168471901.57,
            "status_code": 200,
            "url": "https://tirak-backend.tirak-court.workers.dev/api/notifications"
          },
          "origin": "react-native"
        },
        {
          "timestamp": 1752168474.27,
          "type": "http",
          "category": "xhr",
          "level": "info",
          "data": {
            "end_timestamp": 1752168474269.39,
            "method": "GET",
            "response_body_size": 844,
            "start_timestamp": 1752168471904.94,
            "status_code": 200,
            "url": "https://tirak-backend.tirak-court.workers.dev/api/suppliers/stats"
          },
          "origin": "react-native"
        },
        {
          "timestamp": 1752168475.771,
          "type": "user",
          "category": "touch",
          "level": "info",
          "message": "Touch event within element: Svg",
          "data": {
            "path": [
              {
                "name": "Svg"
              },
              {
                "name": "Settings"
              },
              {
                "name": "View"
              },
              {
                "name": "Animated(View)"
              },
              {
                "name": "TouchableOpacity"
              },
              {
                "name": "View"
              },
              {
                "name": "ScrollView"
              },
              {
                "name": "View"
              },
              {
                "name": "HeaderHeightContext"
              },
              {
                "name": "HeaderShownContext"
              },
              {
                "name": "View"
              },
              {
                "name": "Animated(View)"
              },
              {
                "name": "Animated(Anonymous)"
              },
              {
                "name": "Screen"
              },
              {
                "name": "View"
              },
              {
                "name": "HeaderBackContext"
              },
              {
                "name": "HeaderShownContext"
              },
              {
                "name": "HeaderHeightContext"
              },
              {
                "name": "Animated(Anonymous)"
              },
              {
                "name": "Screen"
              }
            ]
          },
          "origin": "react-native"
        },
        {
          "timestamp": 1752168475.864,
          "type": "default",
          "category": "console",
          "level": "debug",
          "message": "Navigating to: /(supplier)/services Platform: android Dev mode: false",
          "data": {
            "arguments": [
              "Navigating to:",
              "/(supplier)/services",
              "Platform:",
              "android",
              "Dev mode:",
              false],
            "logger": "console"
          },
          "origin": "react-native"
        },
        {
          "timestamp": 1752168475.876,
          "type": "default",
          "category": "console",
          "level": "debug",
          "message": "Navigation successful to: /(supplier)/services",
          "data": {
            "arguments": [
              "Navigation successful to:",
              "/(supplier)/services"
            ],
            "logger": "console"
          },
          "origin": "react-native"
        },
        {
          "timestamp": 1752168475.885,
          "type": "default",
          "category": "console",
          "level": "warning",
          "message": "[Layout children]: No route named \"availability\" exists in nested children: index,analytics/index,availability/add-slot,availability/index,profile/index,services/index",
          "data": {
            "arguments": [
              "[Layout children]: No route named \"availability\" exists in nested children:",
              [
                "index",
                "analytics/index",
                "availability/add-slot",
                "availability/index",
                "profile/index",
                "services/index"
              ]
            ],
            "logger": "console"
          },
          "origin": "react-native"
        },
        {
          "timestamp": 1752168475.885,
          "type": "default",
          "category": "console",
          "level": "warning",
          "message": "[Layout children]: No route named \"services\" exists in nested children: index,analytics/index,availability/add-slot,availability/index,profile/index,services/index",
          "data": {
            "arguments": [
              "[Layout children]: No route named \"services\" exists in nested children:",
              [
                "index",
                "analytics/index",
                "availability/add-slot",
                "availability/index",
                "profile/index",
                "services/index"
              ]
            ],
            "logger": "console"
          },
          "origin": "react-native"
        },
        {
          "timestamp": 1752168475.886,
          "type": "default",
          "category": "console",
          "level": "warning",
          "message": "[Layout children]: No route named \"analytics\" exists in nested children: index,analytics/index,availability/add-slot,availability/index,profile/index,services/index",
          "data": {
            "arguments": [
              "[Layout children]: No route named \"analytics\" exists in nested children:",
              [
                "index",
                "analytics/index",
                "availability/add-slot",
                "availability/index",
                "profile/index",
                "services/index"
              ]
            ],
            "logger": "console"
          },
          "origin": "react-native"
        },
        {
          "timestamp": 1752168475.886,
          "type": "default",
          "category": "console",
          "level": "warning",
          "message": "[Layout children]: No route named \"profile\" exists in nested children: index,analytics/index,availability/add-slot,availability/index,profile/index,services/index",
          "data": {
            "arguments": [
              "[Layout children]: No route named \"profile\" exists in nested children:",
              [
                "index",
                "analytics/index",
                "availability/add-slot",
                "availability/index",
                "profile/index",
                "services/index"
              ]
            ],
            "logger": "console"
          },
          "origin": "react-native"
        },
        {
          "timestamp": 1752168475.886,
          "type": "default",
          "category": "console",
          "level": "warning",
          "message": "[Layout children]: No route named \"settings\" exists in nested children: index,analytics/index,availability/add-slot,availability/index,profile/index,services/index",
          "data": {
            "arguments": [
              "[Layout children]: No route named \"settings\" exists in nested children:",
              [
                "index",
                "analytics/index",
                "availability/add-slot",
                "availability/index",
                "profile/index",
                "services/index"
              ]
            ],
            "logger": "console"
          },
          "origin": "react-native"
        }
      ]
    },
    "contexts": {
      "app": {
        "app_start_time": "2025-07-10T17:27:50.813Z",
        "app_identifier": "com.tirak.pineapple",
        "app_name": "Tirak",
        "app_version": "1.0.0",
        "app_build": "1",
        "in_foreground": true,
        "permissions": {
          "ACCESS_COARSE_LOCATION": "not_granted",
          "ACCESS_FINE_LOCATION": "not_granted",
          "ACCESS_NETWORK_STATE": "granted",
          "CAMERA": "not_granted",
          "DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION": "granted",
          "INTERNET": "granted",
          "READ_EXTERNAL_STORAGE": "not_granted",
          "RECORD_AUDIO": "not_granted",
          "SYSTEM_ALERT_WINDOW": "not_granted",
          "USE_BIOMETRIC": "granted",
          "USE_FINGERPRINT": "granted",
          "VIBRATE": "granted",
          "WRITE_EXTERNAL_STORAGE": "not_granted"
        },
        "type": "app"
      },
      "device": {
        "name": "sdk_gphone64_arm64",
        "family": "sdk_gphone64_arm64",
        "model": "sdk_gphone64_arm64",
        "model_id": "AE3A.240806.036",
        "battery_level": 100,
        "orientation": "portrait",
        "manufacturer": "Google",
        "brand": "google",
        "screen_width_pixels": 1080,
        "screen_height_pixels": 2400,
        "screen_density": 2.625,
        "screen_dpi": 420,
        "online": true,
        "charging": false,
        "low_memory": false,
        "simulator": true,
        "memory_size": 2070597632,
        "free_memory": 865628160,
        "storage_size": 6228115456,
        "free_storage": 1072726016,
        "boot_time": "2025-07-10T15:35:08.033Z",
        "timezone": "Asia/Kolkata",
        "locale": "en_US",
        "processor_count": 4,
        "processor_frequency": 0,
        "archs": [
          "arm64-v8a"
        ],
        "battery_temperature": 25,
        "connection_type": "wifi",
        "id": "eae27f58-2d79-4ffe-86cf-01859a7cfdde",
        "language": "en",
        "type": "device"
      },
      "os": {
        "os": "Android 15",
        "name": "Android",
        "version": "15",
        "build": "AE3A.240806.036",
        "kernel_version": "6.6.30-android15-8-gdd9c02ccfe27-ab11987101-4k",
        "rooted": false,
        "type": "os"
      },
      "ota_updates": {
        "is_enabled": false,
        "type": "ota_updates"
      },
      "replay": {
        "replay_id": "61c1a9cf13d84c87a240b8e328c293cd",
        "type": "replay"
      },
      "trace": {
        "trace_id": "1880224a3cdc4524b4c5f73a1c04e642",
        "span_id": "f3ddff8e24504c88",
        "op": "default",
        "status": "unknown",
        "origin": "manual",
        "type": "trace"
      }
    },
    "culprit": "com.facebook.react.uimanager.ViewManager in updateProperties",
    "environment": "production",
    "exception": {
      "values": [
        {
          "type": "RuntimeException",
          "value": "Exception thrown when executing UIFrameGuarded",
          "module": "java.lang",
          "stacktrace": {
            "frames": [
              {
                "function": "main",
                "module": "com.android.internal.os.ZygoteInit",
                "filename": "ZygoteInit.java",
                "abs_path": "ZygoteInit.java",
                "lineno": 886,
                "in_app": false,
                "data": {
                  "orig_in_app": -1,
                  "category": "system"
                }
              },
              {
                "function": "run",
                "module": "com.android.internal.os.RuntimeInit$MethodAndArgsCaller",
                "filename": "RuntimeInit.java",
                "abs_path": "RuntimeInit.java",
                "lineno": 580,
                "in_app": false,
                "data": {
                  "orig_in_app": -1,
                  "category": "internals"
                }
              },
              {
                "function": "invoke",
                "module": "java.lang.reflect.Method",
                "filename": "Method.java",
                "abs_path": "Method.java",
                "in_app": false,
                "data": {
                  "orig_in_app": -1,
                  "category": "indirection"
                }
              },
              {
                "function": "main",
                "module": "android.app.ActivityThread",
                "filename": "ActivityThread.java",
                "abs_path": "ActivityThread.java",
                "lineno": 8705,
                "in_app": false,
                "data": {
                  "orig_in_app": -1,
                  "category": "threadbase"
                }
              },
              {
                "function": "loop",
                "module": "android.os.Looper",
                "filename": "Looper.java",
                "abs_path": "Looper.java",
                "lineno": 317,
                "in_app": false,
                "data": {
                  "orig_in_app": -1,
                  "category": "threadbase"
                }
              },
              {
                "function": "loopOnce",
                "module": "android.os.Looper",
                "filename": "Looper.java",
                "abs_path": "Looper.java",
                "lineno": 232,
                "in_app": false,
                "data": {
                  "orig_in_app": -1,
                  "category": "indirection"
                }
              },
              {
                "function": "dispatchMessage",
                "module": "android.os.Handler",
                "filename": "Handler.java",
                "abs_path": "Handler.java",
                "lineno": 100,
                "in_app": false,
                "data": {
                  "orig_in_app": -1,
                  "category": "threadbase"
                }
              },
              {
                "function": "handleCallback",
                "module": "android.os.Handler",
                "filename": "Handler.java",
                "abs_path": "Handler.java",
                "lineno": 959,
                "in_app": false,
                "data": {
                  "orig_in_app": -1,
                  "category": "threadbase"
                }
              },
              {
                "function": "run",
                "module": "android.view.Choreographer$FrameDisplayEventReceiver",
                "filename": "Choreographer.java",
                "abs_path": "Choreographer.java",
                "lineno": 1389,
                "in_app": false,
                "data": {
                  "orig_in_app": -1,
                  "category": "indirection"
                }
              },
              {
                "function": "doFrame",
                "module": "android.view.Choreographer",
                "filename": "Choreographer.java",
                "abs_path": "Choreographer.java",
                "lineno": 941,
                "in_app": false,
                "data": {
                  "orig_in_app": -1,
                  "category": "ui"
                }
              },
              {
                "function": "doCallbacks",
                "module": "android.view.Choreographer",
                "filename": "Choreographer.java",
                "abs_path": "Choreographer.java",
                "lineno": 1015,
                "in_app": false,
                "data": {
                  "orig_in_app": -1,
                  "category": "internals"
                }
              },
              {
                "function": "run",
                "module": "android.view.Choreographer$CallbackRecord",
                "filename": "Choreographer.java",
                "abs_path": "Choreographer.java",
                "lineno": 1415,
                "in_app": false,
                "data": {
                  "orig_in_app": -1,
                  "category": "indirection"
                }
              },
              {
                "function": "run",
                "module": "android.view.Choreographer$CallbackRecord",
                "filename": "Choreographer.java",
                "abs_path": "Choreographer.java",
                "lineno": 1404,
                "in_app": false,
                "data": {
                  "orig_in_app": -1,
                  "category": "indirection"
                }
              },
              {
                "function": "doFrame",
                "module": "com.facebook.react.modules.core.ReactChoreographer$$ExternalSyntheticLambda0",
                "filename": "D8$$SyntheticClass",
                "abs_path": "D8$$SyntheticClass",
                "lineno": 0,
                "in_app": false,
                "data": {
                  "orig_in_app": -1
                }
              },
              {
                "function": "$r8$lambda$nSkFhrr5T7rop_XKwzlLov4NLLw",
                "module": "com.facebook.react.modules.core.ReactChoreographer",
                "lineno": 0,
                "in_app": false,
                "data": {
                  "orig_in_app": -1
                }
              },
              {
                "function": "frameCallback$lambda$1",
                "module": "com.facebook.react.modules.core.ReactChoreographer",
                "filename": "ReactChoreographer.kt",
                "abs_path": "ReactChoreographer.kt",
                "lineno": 59,
                "in_app": false,
                "data": {
                  "orig_in_app": -1
                }
              },
              {
                "function": "doFrame",
                "module": "com.facebook.react.uimanager.GuardedFrameCallback",
                "filename": "GuardedFrameCallback.kt",
                "abs_path": "GuardedFrameCallback.kt",
                "lineno": 25,
                "in_app": false,
                "data": {
                  "orig_in_app": -1
                }
              },
              {
                "function": "doFrameGuarded",
                "module": "com.facebook.react.fabric.FabricUIManager$DispatchUIFrameCallback",
                "filename": "FabricUIManager.java",
                "abs_path": "FabricUIManager.java",
                "lineno": 1400,
                "in_app": false,
                "data": {
                  "orig_in_app": -1
                }
              }
            ]
          },
          "raw_stacktrace": {
            "frames": [
              {
                "function": "main",
                "module": "com.android.internal.os.ZygoteInit",
                "filename": "ZygoteInit.java",
                "abs_path": "ZygoteInit.java",
                "lineno": 886,
                "in_app": false,
                "data": {
                  "orig_in_app": -1,
                  "category": "system"
                }
              },
              {
                "function": "run",
                "module": "com.android.internal.os.RuntimeInit$MethodAndArgsCaller",
                "filename": "RuntimeInit.java",
                "abs_path": "RuntimeInit.java",
                "lineno": 580,
                "in_app": false,
                "data": {
                  "orig_in_app": -1,
                  "category": "internals"
                }
              },
              {
                "function": "invoke",
                "module": "java.lang.reflect.Method",
                "filename": "Method.java",
                "abs_path": "Method.java",
                "in_app": false,
                "data": {
                  "orig_in_app": -1,
                  "category": "indirection"
                }
              },
              {
                "function": "main",
                "module": "android.app.ActivityThread",
                "filename": "ActivityThread.java",
                "abs_path": "ActivityThread.java",
                "lineno": 8705,
                "in_app": false,
                "data": {
                  "orig_in_app": -1,
                  "category": "threadbase"
                }
              },
              {
                "function": "loop",
                "module": "android.os.Looper",
                "filename": "Looper.java",
                "abs_path": "Looper.java",
                "lineno": 317,
                "in_app": false,
                "data": {
                  "orig_in_app": -1,
                  "category": "threadbase"
                }
              },
              {
                "function": "loopOnce",
                "module": "android.os.Looper",
                "filename": "Looper.java",
                "abs_path": "Looper.java",
                "lineno": 232,
                "in_app": false,
                "data": {
                  "orig_in_app": -1,
                  "category": "indirection"
                }
              },
              {
                "function": "dispatchMessage",
                "module": "android.os.Handler",
                "filename": "Handler.java",
                "abs_path": "Handler.java",
                "lineno": 100,
                "in_app": false,
                "data": {
                  "orig_in_app": -1,
                  "category": "threadbase"
                }
              },
              {
                "function": "handleCallback",
                "module": "android.os.Handler",
                "filename": "Handler.java",
                "abs_path": "Handler.java",
                "lineno": 959,
                "in_app": false,
                "data": {
                  "orig_in_app": -1,
                  "category": "threadbase"
                }
              },
              {
                "function": "run",
                "module": "android.view.Choreographer$FrameDisplayEventReceiver",
                "filename": "Choreographer.java",
                "abs_path": "Choreographer.java",
                "lineno": 1389,
                "in_app": false,
                "data": {
                  "orig_in_app": -1,
                  "category": "indirection"
                }
              },
              {
                "function": "doFrame",
                "module": "android.view.Choreographer",
                "filename": "Choreographer.java",
                "abs_path": "Choreographer.java",
                "lineno": 941,
                "in_app": false,
                "data": {
                  "orig_in_app": -1,
                  "category": "ui"
                }
              },
              {
                "function": "doCallbacks",
                "module": "android.view.Choreographer",
                "filename": "Choreographer.java",
                "abs_path": "Choreographer.java",
                "lineno": 1015,
                "in_app": false,
                "data": {
                  "orig_in_app": -1,
                  "category": "internals"
                }
              },
              {
                "function": "run",
                "module": "android.view.Choreographer$CallbackRecord",
                "filename": "Choreographer.java",
                "abs_path": "Choreographer.java",
                "lineno": 1415,
                "in_app": false,
                "data": {
                  "orig_in_app": -1,
                  "category": "indirection"
                }
              },
              {
                "function": "run",
                "module": "android.view.Choreographer$CallbackRecord",
                "filename": "Choreographer.java",
                "abs_path": "Choreographer.java",
                "lineno": 1404,
                "in_app": false,
                "data": {
                  "orig_in_app": -1,
                  "category": "indirection"
                }
              },
              {
                "function": "doFrame",
                "module": "com.facebook.react.modules.core.ReactChoreographer$$ExternalSyntheticLambda0",
                "filename": "D8$$SyntheticClass",
                "abs_path": "D8$$SyntheticClass",
                "lineno": 0,
                "in_app": false,
                "data": {
                  "orig_in_app": -1
                }
              },
              {
                "function": "$r8$lambda$nSkFhrr5T7rop_XKwzlLov4NLLw",
                "module": "com.facebook.react.modules.core.ReactChoreographer",
                "lineno": 0,
                "in_app": false,
                "data": {
                  "orig_in_app": -1
                }
              },
              {
                "function": "frameCallback$lambda$1",
                "module": "com.facebook.react.modules.core.ReactChoreographer",
                "filename": "ReactChoreographer.kt",
                "abs_path": "ReactChoreographer.kt",
                "lineno": 59,
                "in_app": false,
                "data": {
                  "orig_in_app": -1
                }
              },
              {
                "function": "doFrame",
                "module": "com.facebook.react.uimanager.GuardedFrameCallback",
                "filename": "GuardedFrameCallback.kt",
                "abs_path": "GuardedFrameCallback.kt",
                "lineno": 25,
                "in_app": false,
                "data": {
                  "orig_in_app": -1
                }
              },
              {
                "function": "doFrameGuarded",
                "module": "com.facebook.react.fabric.FabricUIManager$DispatchUIFrameCallback",
                "filename": "FabricUIManager.java",
                "abs_path": "FabricUIManager.java",
                "lineno": 1400,
                "in_app": false,
                "data": {
                  "orig_in_app": -1
                }
              }
            ]
          },
          "thread_id": 2,
          "mechanism": {
            "type": "UncaughtExceptionHandler",
            "handled": false
          },
          "raw_module": "java.lang",
          "raw_type": "RuntimeException"
        },
        {
          "type": "NullPointerException",
          "value": "Attempt to invoke interface method 'void com.facebook.react.uimanager.ViewManagerDelegate.setProperty(android.view.View, java.lang.String, java.lang.Object)' on a null object reference",
          "module": "java.lang",
          "stacktrace": {
            "frames": [
              {
                "function": "main",
                "module": "com.android.internal.os.ZygoteInit",
                "filename": "ZygoteInit.java",
                "abs_path": "ZygoteInit.java",
                "lineno": 886,
                "in_app": false,
                "data": {
                  "orig_in_app": -1,
                  "category": "system"
                }
              },
              {
                "function": "run",
                "module": "com.android.internal.os.RuntimeInit$MethodAndArgsCaller",
                "filename": "RuntimeInit.java",
                "abs_path": "RuntimeInit.java",
                "lineno": 580,
                "in_app": false,
                "data": {
                  "orig_in_app": -1,
                  "category": "internals"
                }
              },
              {
                "function": "invoke",
                "module": "java.lang.reflect.Method",
                "filename": "Method.java",
                "abs_path": "Method.java",
                "in_app": false,
                "data": {
                  "orig_in_app": -1,
                  "category": "indirection"
                }
              },
              {
                "function": "main",
                "module": "android.app.ActivityThread",
                "filename": "ActivityThread.java",
                "abs_path": "ActivityThread.java",
                "lineno": 8705,
                "in_app": false,
                "data": {
                  "orig_in_app": -1,
                  "category": "threadbase"
                }
              },
              {
                "function": "loop",
                "module": "android.os.Looper",
                "filename": "Looper.java",
                "abs_path": "Looper.java",
                "lineno": 317,
                "in_app": false,
                "data": {
                  "orig_in_app": -1,
                  "category": "threadbase"
                }
              },
              {
                "function": "loopOnce",
                "module": "android.os.Looper",
                "filename": "Looper.java",
                "abs_path": "Looper.java",
                "lineno": 232,
                "in_app": false,
                "data": {
                  "orig_in_app": -1,
                  "category": "indirection"
                }
              },
              {
                "function": "dispatchMessage",
                "module": "android.os.Handler",
                "filename": "Handler.java",
                "abs_path": "Handler.java",
                "lineno": 100,
                "in_app": false,
                "data": {
                  "orig_in_app": -1,
                  "category": "threadbase"
                }
              },
              {
                "function": "handleCallback",
                "module": "android.os.Handler",
                "filename": "Handler.java",
                "abs_path": "Handler.java",
                "lineno": 959,
                "in_app": false,
                "data": {
                  "orig_in_app": -1,
                  "category": "threadbase"
                }
              },
              {
                "function": "run",
                "module": "android.view.Choreographer$FrameDisplayEventReceiver",
                "filename": "Choreographer.java",
                "abs_path": "Choreographer.java",
                "lineno": 1389,
                "in_app": false,
                "data": {
                  "orig_in_app": -1,
                  "category": "indirection"
                }
              },
              {
                "function": "doFrame",
                "module": "android.view.Choreographer",
                "filename": "Choreographer.java",
                "abs_path": "Choreographer.java",
                "lineno": 941,
                "in_app": false,
                "data": {
                  "orig_in_app": -1,
                  "category": "ui"
                }
              },
              {
                "function": "doCallbacks",
                "module": "android.view.Choreographer",
                "filename": "Choreographer.java",
                "abs_path": "Choreographer.java",
                "lineno": 1015,
                "in_app": false,
                "data": {
                  "orig_in_app": -1,
                  "category": "internals"
                }
              },
              {
                "function": "run",
                "module": "android.view.Choreographer$CallbackRecord",
                "filename": "Choreographer.java",
                "abs_path": "Choreographer.java",
                "lineno": 1415,
                "in_app": false,
                "data": {
                  "orig_in_app": -1,
                  "category": "indirection"
                }
              },
              {
                "function": "run",
                "module": "android.view.Choreographer$CallbackRecord",
                "filename": "Choreographer.java",
                "abs_path": "Choreographer.java",
                "lineno": 1404,
                "in_app": false,
                "data": {
                  "orig_in_app": -1,
                  "category": "indirection"
                }
              },
              {
                "function": "doFrame",
                "module": "com.facebook.react.modules.core.ReactChoreographer$$ExternalSyntheticLambda0",
                "filename": "D8$$SyntheticClass",
                "abs_path": "D8$$SyntheticClass",
                "lineno": 0,
                "in_app": false,
                "data": {
                  "orig_in_app": -1
                }
              },
              {
                "function": "$r8$lambda$nSkFhrr5T7rop_XKwzlLov4NLLw",
                "module": "com.facebook.react.modules.core.ReactChoreographer",
                "lineno": 0,
                "in_app": false,
                "data": {
                  "orig_in_app": -1
                }
              },
              {
                "function": "frameCallback$lambda$1",
                "module": "com.facebook.react.modules.core.ReactChoreographer",
                "filename": "ReactChoreographer.kt",
                "abs_path": "ReactChoreographer.kt",
                "lineno": 59,
                "in_app": false,
                "data": {
                  "orig_in_app": -1
                }
              },
              {
                "function": "doFrame",
                "module": "com.facebook.react.uimanager.GuardedFrameCallback",
                "filename": "GuardedFrameCallback.kt",
                "abs_path": "GuardedFrameCallback.kt",
                "lineno": 25,
                "in_app": false,
                "data": {
                  "orig_in_app": -1
                }
              },
              {
                "function": "doFrameGuarded",
                "module": "com.facebook.react.fabric.FabricUIManager$DispatchUIFrameCallback",
                "filename": "FabricUIManager.java",
                "abs_path": "FabricUIManager.java",
                "lineno": 1395,
                "in_app": false,
                "data": {
                  "orig_in_app": -1
                }
              },
              {
                "function": "dispatchPreMountItems",
                "module": "com.facebook.react.fabric.mounting.MountItemDispatcher",
                "filename": "MountItemDispatcher.java",
                "abs_path": "MountItemDispatcher.java",
                "lineno": 322,
                "in_app": false,
                "data": {
                  "orig_in_app": -1
                }
              },
              {
                "function": "dispatchPreMountItemsImpl",
                "module": "com.facebook.react.fabric.mounting.MountItemDispatcher",
                "filename": "MountItemDispatcher.java",
                "abs_path": "MountItemDispatcher.java",
                "lineno": 349,
                "in_app": false,
                "data": {
                  "orig_in_app": -1
                }
              },
              {
                "function": "executeOrEnqueue",
                "module": "com.facebook.react.fabric.mounting.MountItemDispatcher",
                "filename": "MountItemDispatcher.java",
                "abs_path": "MountItemDispatcher.java",
                "lineno": 370,
                "in_app": false,
                "data": {
                  "orig_in_app": -1
                }
              },
              {
                "function": "execute",
                "module": "com.facebook.react.fabric.mounting.mountitems.PreAllocateViewMountItem",
                "filename": "PreAllocateViewMountItem.kt",
                "abs_path": "PreAllocateViewMountItem.kt",
                "lineno": 38,
                "in_app": false,
                "data": {
                  "orig_in_app": -1
                }
              },
              {
                "function": "preallocateView",
                "module": "com.facebook.react.fabric.mounting.SurfaceMountingManager",
                "filename": "SurfaceMountingManager.java",
                "abs_path": "SurfaceMountingManager.java",
                "lineno": 1078,
                "in_app": false,
                "data": {
                  "orig_in_app": -1
                }
              },
              {
                "function": "createViewUnsafe",
                "module": "com.facebook.react.fabric.mounting.SurfaceMountingManager",
                "filename": "SurfaceMountingManager.java",
                "abs_path": "SurfaceMountingManager.java",
                "lineno": 674,
                "in_app": false,
                "data": {
                  "orig_in_app": -1
                }
              },
              {
                "function": "createView",
                "module": "com.facebook.react.uimanager.ViewManager",
                "filename": "ViewManager.java",
                "abs_path": "ViewManager.java",
                "lineno": 145,
                "in_app": false,
                "data": {
                  "orig_in_app": -1
                }
              },
              {
                "function": "createViewInstance",
                "module": "com.facebook.react.uimanager.ViewManager",
                "filename": "ViewManager.java",
                "abs_path": "ViewManager.java",
                "lineno": 218,
                "in_app": false,
                "data": {
                  "orig_in_app": -1
                }
              },
              {
                "function": "updateProperties",
                "module": "com.facebook.react.uimanager.ViewManager",
                "filename": "ViewManager.java",
                "abs_path": "ViewManager.java",
                "lineno": 100,
                "in_app": false,
                "data": {
                  "orig_in_app": -1
                }
              }
            ]
          },
          "raw_stacktrace": {
            "frames": [
              {
                "function": "main",
                "module": "com.android.internal.os.ZygoteInit",
                "filename": "ZygoteInit.java",
                "abs_path": "ZygoteInit.java",
                "lineno": 886,
                "in_app": false,
                "data": {
                  "orig_in_app": -1,
                  "category": "system"
                }
              },
              {
                "function": "run",
                "module": "com.android.internal.os.RuntimeInit$MethodAndArgsCaller",
                "filename": "RuntimeInit.java",
                "abs_path": "RuntimeInit.java",
                "lineno": 580,
                "in_app": false,
                "data": {
                  "orig_in_app": -1,
                  "category": "internals"
                }
              },
              {
                "function": "invoke",
                "module": "java.lang.reflect.Method",
                "filename": "Method.java",
                "abs_path": "Method.java",
                "in_app": false,
                "data": {
                  "orig_in_app": -1,
                  "category": "indirection"
                }
              },
              {
                "function": "main",
                "module": "android.app.ActivityThread",
                "filename": "ActivityThread.java",
                "abs_path": "ActivityThread.java",
                "lineno": 8705,
                "in_app": false,
                "data": {
                  "orig_in_app": -1,
                  "category": "threadbase"
                }
              },
              {
                "function": "loop",
                "module": "android.os.Looper",
                "filename": "Looper.java",
                "abs_path": "Looper.java",
                "lineno": 317,
                "in_app": false,
                "data": {
                  "orig_in_app": -1,
                  "category": "threadbase"
                }
              },
              {
                "function": "loopOnce",
                "module": "android.os.Looper",
                "filename": "Looper.java",
                "abs_path": "Looper.java",
                "lineno": 232,
                "in_app": false,
                "data": {
                  "orig_in_app": -1,
                  "category": "indirection"
                }
              },
              {
                "function": "dispatchMessage",
                "module": "android.os.Handler",
                "filename": "Handler.java",
                "abs_path": "Handler.java",
                "lineno": 100,
                "in_app": false,
                "data": {
                  "orig_in_app": -1,
                  "category": "threadbase"
                }
              },
              {
                "function": "handleCallback",
                "module": "android.os.Handler",
                "filename": "Handler.java",
                "abs_path": "Handler.java",
                "lineno": 959,
                "in_app": false,
                "data": {
                  "orig_in_app": -1,
                  "category": "threadbase"
                }
              },
              {
                "function": "run",
                "module": "android.view.Choreographer$FrameDisplayEventReceiver",
                "filename": "Choreographer.java",
                "abs_path": "Choreographer.java",
                "lineno": 1389,
                "in_app": false,
                "data": {
                  "orig_in_app": -1,
                  "category": "indirection"
                }
              },
              {
                "function": "doFrame",
                "module": "android.view.Choreographer",
                "filename": "Choreographer.java",
                "abs_path": "Choreographer.java",
                "lineno": 941,
                "in_app": false,
                "data": {
                  "orig_in_app": -1,
                  "category": "ui"
                }
              },
              {
                "function": "doCallbacks",
                "module": "android.view.Choreographer",
                "filename": "Choreographer.java",
                "abs_path": "Choreographer.java",
                "lineno": 1015,
                "in_app": false,
                "data": {
                  "orig_in_app": -1,
                  "category": "internals"
                }
              },
              {
                "function": "run",
                "module": "android.view.Choreographer$CallbackRecord",
                "filename": "Choreographer.java",
                "abs_path": "Choreographer.java",
                "lineno": 1415,
                "in_app": false,
                "data": {
                  "orig_in_app": -1,
                  "category": "indirection"
                }
              },
              {
                "function": "run",
                "module": "android.view.Choreographer$CallbackRecord",
                "filename": "Choreographer.java",
                "abs_path": "Choreographer.java",
                "lineno": 1404,
                "in_app": false,
                "data": {
                  "orig_in_app": -1,
                  "category": "indirection"
                }
              },
              {
                "function": "doFrame",
                "module": "com.facebook.react.modules.core.ReactChoreographer$$ExternalSyntheticLambda0",
                "filename": "D8$$SyntheticClass",
                "abs_path": "D8$$SyntheticClass",
                "lineno": 0,
                "in_app": false,
                "data": {
                  "orig_in_app": -1
                }
              },
              {
                "function": "$r8$lambda$nSkFhrr5T7rop_XKwzlLov4NLLw",
                "module": "com.facebook.react.modules.core.ReactChoreographer",
                "lineno": 0,
                "in_app": false,
                "data": {
                  "orig_in_app": -1
                }
              },
              {
                "function": "frameCallback$lambda$1",
                "module": "com.facebook.react.modules.core.ReactChoreographer",
                "filename": "ReactChoreographer.kt",
                "abs_path": "ReactChoreographer.kt",
                "lineno": 59,
                "in_app": false,
                "data": {
                  "orig_in_app": -1
                }
              },
              {
                "function": "doFrame",
                "module": "com.facebook.react.uimanager.GuardedFrameCallback",
                "filename": "GuardedFrameCallback.kt",
                "abs_path": "GuardedFrameCallback.kt",
                "lineno": 25,
                "in_app": false,
                "data": {
                  "orig_in_app": -1
                }
              },
              {
                "function": "doFrameGuarded",
                "module": "com.facebook.react.fabric.FabricUIManager$DispatchUIFrameCallback",
                "filename": "FabricUIManager.java",
                "abs_path": "FabricUIManager.java",
                "lineno": 1395,
                "in_app": false,
                "data": {
                  "orig_in_app": -1
                }
              },
              {
                "function": "dispatchPreMountItems",
                "module": "com.facebook.react.fabric.mounting.MountItemDispatcher",
                "filename": "MountItemDispatcher.java",
                "abs_path": "MountItemDispatcher.java",
                "lineno": 322,
                "in_app": false,
                "data": {
                  "orig_in_app": -1
                }
              },
              {
                "function": "dispatchPreMountItemsImpl",
                "module": "com.facebook.react.fabric.mounting.MountItemDispatcher",
                "filename": "MountItemDispatcher.java",
                "abs_path": "MountItemDispatcher.java",
                "lineno": 349,
                "in_app": false,
                "data": {
                  "orig_in_app": -1
                }
              },
              {
                "function": "executeOrEnqueue",
                "module": "com.facebook.react.fabric.mounting.MountItemDispatcher",
                "filename": "MountItemDispatcher.java",
                "abs_path": "MountItemDispatcher.java",
                "lineno": 370,
                "in_app": false,
                "data": {
                  "orig_in_app": -1
                }
              },
              {
                "function": "execute",
                "module": "com.facebook.react.fabric.mounting.mountitems.PreAllocateViewMountItem",
                "filename": "PreAllocateViewMountItem.kt",
                "abs_path": "PreAllocateViewMountItem.kt",
                "lineno": 38,
                "in_app": false,
                "data": {
                  "orig_in_app": -1
                }
              },
              {
                "function": "preallocateView",
                "module": "com.facebook.react.fabric.mounting.SurfaceMountingManager",
                "filename": "SurfaceMountingManager.java",
                "abs_path": "SurfaceMountingManager.java",
                "lineno": 1078,
                "in_app": false,
                "data": {
                  "orig_in_app": -1
                }
              },
              {
                "function": "createViewUnsafe",
                "module": "com.facebook.react.fabric.mounting.SurfaceMountingManager",
                "filename": "SurfaceMountingManager.java",
                "abs_path": "SurfaceMountingManager.java",
                "lineno": 674,
                "in_app": false,
                "data": {
                  "orig_in_app": -1
                }
              },
              {
                "function": "createView",
                "module": "com.facebook.react.uimanager.ViewManager",
                "filename": "ViewManager.java",
                "abs_path": "ViewManager.java",
                "lineno": 145,
                "in_app": false,
                "data": {
                  "orig_in_app": -1
                }
              },
              {
                "function": "createViewInstance",
                "module": "com.facebook.react.uimanager.ViewManager",
                "filename": "ViewManager.java",
                "abs_path": "ViewManager.java",
                "lineno": 218,
                "in_app": false,
                "data": {
                  "orig_in_app": -1
                }
              },
              {
                "function": "updateProperties",
                "module": "com.facebook.react.uimanager.ViewManager",
                "filename": "ViewManager.java",
                "abs_path": "ViewManager.java",
                "lineno": 100,
                "in_app": false,
                "data": {
                  "orig_in_app": -1
                }
              }
            ]
          },
          "thread_id": 2,
          "raw_module": "java.lang",
          "raw_type": "NullPointerException"
        }
      ]
    },
    "fingerprint": [
      "{{ default }}"
    ],
    "grouping_config": {
      "enhancements": "KLUv_SAYwQAAkwKRs25ld3N0eWxlOjIwMjMtMDEtMTGQ#KLUv_SAYwQAAkwKRs25ld3N0eWxlOjIwMjMtMDEtMTGQ#KLUv_SAYwQAAkwKRs25ld3N0eWxlOjIwMjMtMDEtMTGQ",
      "id": "newstyle:2023-01-11"
    },
    "hashes": [
      "686eb57e4b70ce6e2c5bd36ea9ddb602"
    ],
    "ingest_path": [
      {
        "version": "25.6.2",
        "public_key": "XE7QiyuNlja9PZ7I9qJlwQotzecWrUIN91BAO7Q5R38"
      }
    ],
    "key_id": "4650000",
    "level": "fatal",
    "location": "ViewManager.java",
    "logger": "",
    "metadata": {
      "filename": "ViewManager.java",
      "function": "updateProperties",
      "in_app_frame_mix": "system-only",
      "type": "NullPointerException",
      "value": "Attempt to invoke interface method 'void com.facebook.react.uimanager.ViewManagerDelegate.setProperty(android.view.View, java.lang.String, java.lang.Object)' on a null object reference"
    },
    "nodestore_insert": 1752168482.0609,
    "received": 1752168476.57575,
    "sdk": {
      "name": "sentry.java.android.react-native",
      "version": "7.22.6",
      "integrations": [
        "UncaughtExceptionHandler",
        "ShutdownHook",
        "SendCachedEnvelope",
        "Ndk",
        "AnrV2",
        "ActivityLifecycle",
        "ActivityBreadcrumbs",
        "UserInteraction",
        "AppComponentsBreadcrumbs",
        "Replay",
        "AppLifecycle",
        "SystemEventsBreadcrumbs",
        "NetworkBreadcrumbs"
      ],
      "packages": [
        {
          "name": "maven:io.sentry:sentry",
          "version": "7.22.6"
        },
        {
          "name": "maven:io.sentry:sentry-android-core",
          "version": "7.22.6"
        },
        {
          "name": "npm:@sentry/react-native",
          "version": "6.17.0"
        },
        {
          "name": "maven:io.sentry:sentry-android-ndk",
          "version": "7.22.6"
        },
        {
          "name": "maven:io.sentry:sentry-android-replay",
          "version": "7.22.6"
        }
      ]
    },
    "timestamp": 1752168475.901,
    "title": "NullPointerException: Attempt to invoke interface method 'void com.facebook.react.uimanager.ViewManagerDelegate.setProperty(android.view.View, java.lang.String, java.lang.Object)' on a null object reference",
    "type": "error",
    "user": {
      "id": "eae27f58-2d79-4ffe-86cf-01859a7cfdde",
      "ip_address": "*************",
      "sentry_user": "id:eae27f58-2d79-4ffe-86cf-01859a7cfdde",
      "geo": {
        "country_code": "IN",
        "city": "Siddhapur",
        "subdivision": "Gujarat",
        "region": "India"
      }
    },
    "version": "7"
  }

  
  # NullPointerException: Attempt to invoke interface method 'void com.facebook.react.uimanager.ViewManagerDelegate.setProperty(android.view.View, java.lang.String, java.lang.Object)' on a null object reference

**Issue ID:** 6738275560
**Project:** react-native
**Date:** 10/07/2025, 22:57:55
## Issue Summary
NullPointerException: Null ViewManagerDelegate during UI property update
**What's wrong:** A **NullPointerException** occurred when trying to set a property on a **null** `ViewManagerDelegate` during UI updates.
**Possible cause:** This may be due to a race condition where a view is unmounted or invalidated before its properties are updated, leading to a null delegate.

## Tags

- **device:** sdk_gphone64_arm64
- **device.class:** low
- **device.family:** sdk_gphone64_arm64
- **dist:** 1
- **environment:** production
- **event.environment:** java
- **event.origin:** android
- **isSideLoaded:** true
- **level:** fatal
- **os:** Android 15
- **os.name:** Android
- **os.rooted:** no
- **release:** com.tirak.pineapple@1.0.0+1
- **user:** id:eae27f58-2d79-4ffe-86cf-01859a7cfdde

## Exceptions

### Exception 1
**Type:** RuntimeException
**Value:** Exception thrown when executing UIFrameGuarded

#### Stacktrace

```
 doFrameGuarded in FabricUIManager.java [Line 1400, column null] (Not in app)
------
 doFrame in GuardedFrameCallback.kt [Line 25, column null] (Not in app)
------
 frameCallback$lambda$1 in ReactChoreographer.kt [Line 59, column null] (Not in app)
------
 $r8$lambda$nSkFhrr5T7rop_XKwzlLov4NLLw in unknown file [Line 0, column null] (Not in app)
------
 doFrame in D8$$SyntheticClass [Line 0, column null] (Not in app)
------
 run in Choreographer.java [Line 1404, column null] (Not in app)
------
 run in Choreographer.java [Line 1415, column null] (Not in app)
------
 doCallbacks in Choreographer.java [Line 1015, column null] (Not in app)
------
 doFrame in Choreographer.java [Line 941, column null] (Not in app)
------
 run in Choreographer.java [Line 1389, column null] (Not in app)
------
 handleCallback in Handler.java [Line 959, column null] (Not in app)
------
 dispatchMessage in Handler.java [Line 100, column null] (Not in app)
------
 loopOnce in Looper.java [Line 232, column null] (Not in app)
------
 loop in Looper.java [Line 317, column null] (Not in app)
------
 main in ActivityThread.java [Line 8705, column null] (Not in app)
------
 invoke in Method.java [Line null, column null] (Not in app)
------
```
### Exception 2
**Type:** NullPointerException
**Value:** Attempt to invoke interface method 'void com.facebook.react.uimanager.ViewManagerDelegate.setProperty(android.view.View, java.lang.String, java.lang.Object)' on a null object reference

#### Stacktrace

```
 updateProperties in ViewManager.java [Line 100, column null] (Not in app)
 createViewInstance in ViewManager.java [Line 218, column null] (Not in app)
 createView in ViewManager.java [Line 145, column null] (Not in app)
 createViewUnsafe in SurfaceMountingManager.java [Line 674, column null] (Not in app)
 preallocateView in SurfaceMountingManager.java [Line 1078, column null] (Not in app)
 execute in PreAllocateViewMountItem.kt [Line 38, column null] (Not in app)
 executeOrEnqueue in MountItemDispatcher.java [Line 370, column null] (Not in app)
 dispatchPreMountItemsImpl in MountItemDispatcher.java [Line 349, column null] (Not in app)
 dispatchPreMountItems in MountItemDispatcher.java [Line 322, column null] (Not in app)
 doFrameGuarded in FabricUIManager.java [Line 1395, column null] (Not in app)
 doFrame in GuardedFrameCallback.kt [Line 25, column null] (Not in app)
 frameCallback$lambda$1 in ReactChoreographer.kt [Line 59, column null] (Not in app)
 $r8$lambda$nSkFhrr5T7rop_XKwzlLov4NLLw in unknown file [Line 0, column null] (Not in app)
 doFrame in D8$$SyntheticClass [Line 0, column null] (Not in app)
 run in Choreographer.java [Line 1404, column null] (Not in app)
 run in Choreographer.java [Line 1415, column null] (Not in app)
```
