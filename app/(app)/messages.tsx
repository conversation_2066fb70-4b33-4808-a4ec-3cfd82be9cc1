import React, { useState } from 'react';
import { View, StyleSheet, ScrollView, TouchableOpacity, TextInput, Animated } from 'react-native';
import { router } from 'expo-router';
import { useAuthStore } from '@/stores/auth-store';
import { Card } from '@/components/ui/Card';
import { ProfileImage } from '@/components/ui/ProfileImage';
import { RadialGradient } from '@/components/ui/RadialGradient';
import { Heading, Body, Caption } from '@/components/ui/Typography';
import { designTokens, componentTokens } from '@/constants/design-tokens';
import { Search, MoreVertical, Phone, Video } from 'lucide-react-native';

// TypeScript interfaces for type safety and component architecture
interface ConversationParticipant {
  id: string;
  name: string;
  image: string;
  role: 'customer' | 'companion';
  online: boolean;
  lastSeen?: string;
}

interface Conversation {
  id: string;
  companionName: string;
  companionImage: string;
  lastMessage: string;
  timestamp: string;
  unreadCount: number;
  online: boolean;
  isTyping: boolean;
  participants?: ConversationParticipant[];
  bookingId?: string;
}

interface ConversationItemProps {
  conversation: Conversation;
  onPress?: () => void;
  onCall?: () => void;
  onVideoCall?: () => void;
}

interface TypingIndicatorProps {
  visible: boolean;
}

interface SearchHeaderProps {
  searchQuery: string;
  onSearchChange: (query: string) => void;
}

// Mock data for conversations with proper typing
const mockConversations: Conversation[] = [
  {
    id: '1',
    companionName: 'Nisa Thanakit',
    companionImage: 'https://images.unsplash.com/photo-1529626455594-4ff0802cfb7e?q=80&w=1000&auto=format&fit=crop',
    lastMessage: 'Looking forward to our tour tomorrow! I have some great spots planned.',
    timestamp: '2 min ago',
    unreadCount: 2,
    online: true,
    isTyping: false,
  },
  {
    id: '2',
    companionName: 'Somchai Kittisak',
    companionImage: 'https://images.unsplash.com/photo-1506794778202-cad84cf45f1d?q=80&w=1000&auto=format&fit=crop',
    lastMessage: 'Thank you for the booking! See you at the beach.',
    timestamp: '1 hour ago',
    unreadCount: 0,
    online: false,
    isTyping: false,
  },
  {
    id: '3',
    companionName: 'Malee Siriwan',
    companionImage: 'https://images.unsplash.com/photo-1580489944761-15a19d654956?q=80&w=1000&auto=format&fit=crop',
    lastMessage: 'The temple tour was amazing! Thank you so much.',
    timestamp: '2 days ago',
    unreadCount: 0,
    online: true,
    isTyping: true,
  },
  {
    id: '4',
    companionName: 'Apinya Charoensuk',
    companionImage: 'https://images.unsplash.com/photo-1494790108377-be9c29b29330?q=80&w=1000&auto=format&fit=crop',
    lastMessage: 'Hi! I would love to show you around Bangkok. When are you available?',
    timestamp: '3 days ago',
    unreadCount: 1,
    online: false,
    isTyping: false,
  },
];

// Component: Typing Indicator with organic motion
const TypingIndicator: React.FC<TypingIndicatorProps> = ({ visible }) => {
  if (!visible) return null;

  return (
    <View style={styles.typingIndicator}>
      <View style={styles.typingDots}>
        <View style={[styles.typingDot, styles.typingDot1]} />
        <View style={[styles.typingDot, styles.typingDot2]} />
        <View style={[styles.typingDot, styles.typingDot3]} />
      </View>
      <Caption style={styles.typingText}>typing...</Caption>
    </View>
  );
};

// Component: Conversation Item with design token integration
const ConversationItem: React.FC<ConversationItemProps> = ({
  conversation,
  onPress,
  onCall,
  onVideoCall
}) => {
  return (
    <TouchableOpacity
      style={styles.conversationItem}
      onPress={onPress}
      activeOpacity={0.7}
      accessibilityRole="button"
      accessibilityLabel={`Conversation with ${conversation.companionName}`}
      accessibilityHint="Tap to open chat"
    >
      <Card style={styles.conversationCard}>
        <View style={styles.conversationContent}>
          <View style={styles.avatarContainer}>
            <ProfileImage
              uri={conversation.companionImage}
              size="medium"
              online={conversation.online}
            />
            {conversation.unreadCount > 0 && (
              <View style={styles.unreadBadge}>
                <Caption style={styles.unreadCount}>
                  {conversation.unreadCount > 9 ? '9+' : conversation.unreadCount}
                </Caption>
              </View>
            )}
          </View>

          <View style={styles.messageContent}>
            <View style={styles.messageHeader}>
              <Body style={styles.companionName}>{conversation.companionName}</Body>
              <Caption style={styles.timestamp}>{conversation.timestamp}</Caption>
            </View>

            <View style={styles.messagePreview}>
              {conversation.isTyping ? (
                <TypingIndicator visible={conversation.isTyping} />
              ) : (
                <Caption
                  style={[
                    styles.lastMessage,
                    conversation.unreadCount > 0 && styles.unreadMessage
                  ]}
                  numberOfLines={1}
                >
                  {conversation.lastMessage}
                </Caption>
              )}
            </View>
          </View>

          <View style={styles.conversationActions}>
            <TouchableOpacity
              style={styles.actionButton}
              onPress={onCall}
              accessibilityRole="button"
              accessibilityLabel="Call"
            >
              <Phone size={18} color={designTokens.colors.semantic.accent} />
            </TouchableOpacity>
            <TouchableOpacity
              style={styles.actionButton}
              onPress={onVideoCall}
              accessibilityRole="button"
              accessibilityLabel="Video call"
            >
              <Video size={18} color={designTokens.colors.semantic.accent} />
            </TouchableOpacity>
          </View>
        </View>
      </Card>
    </TouchableOpacity>
  );
};

// Component: Search Header with design tokens
const SearchHeader: React.FC<SearchHeaderProps> = ({ searchQuery, onSearchChange }) => (
  <View style={styles.header}>
    <Heading style={styles.headerTitle}>Messages</Heading>

    <Card style={styles.searchCard}>
      <View style={styles.searchContainer}>
        <Search size={20} color={designTokens.colors.semantic.textSecondary} />
        <TextInput
          style={styles.searchInput}
          placeholder="Search conversations..."
          placeholderTextColor={designTokens.colors.semantic.textSecondary}
          value={searchQuery}
          onChangeText={onSearchChange}
          accessibilityLabel="Search conversations"
          accessibilityHint="Type to search through your conversations"
        />
      </View>
    </Card>
  </View>
);

// Component: Empty State with design tokens
const EmptyState: React.FC<{ searchQuery: string }> = ({ searchQuery }) => (
  <Card style={styles.emptyState}>
    <Body style={styles.emptyStateTitle}>No conversations found</Body>
    <Caption style={styles.emptyStateText}>
      {searchQuery
        ? `No conversations match "${searchQuery}"`
        : "Start chatting with companions to see your conversations here."
      }
    </Caption>
  </Card>
);

export default function MessagesScreen() {
  const { user } = useAuthStore();
  const [searchQuery, setSearchQuery] = useState('');

  const filteredConversations = mockConversations.filter(conversation =>
    conversation.companionName.toLowerCase().includes(searchQuery.toLowerCase())
  );

  return (
    <RadialGradient variant="appBackground" style={styles.container}>
      <SearchHeader searchQuery={searchQuery} onSearchChange={setSearchQuery} />

      <ScrollView
        style={styles.content}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.scrollContent}
      >
        {filteredConversations.length > 0 ? (
          filteredConversations.map((conversation) => (
            <ConversationItem
              key={conversation.id}
              conversation={conversation}
              onPress={() => {
                // Navigate to chat screen with companion ID
                // console.log('Navigate to chat:', conversation.id);
                router.push(`/chat/${conversation.id}`);
              }}
              onCall={() => {
                // Handle call action
                // console.log('Call:', conversation.companionName);
              }}
              onVideoCall={() => {
                // Handle video call action
                // console.log('Video call:', conversation.companionName);
              }}
            />
          ))
        ) : (
          <EmptyState searchQuery={searchQuery} />
        )}
      </ScrollView>
    </RadialGradient>
  );
}

// Styles using design tokens for consistency and maintainability
const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    paddingHorizontal: designTokens.spacing.scale.lg,
    paddingTop: designTokens.spacing.scale.lg,
    paddingBottom: designTokens.spacing.scale.md,
    gap: designTokens.spacing.scale.md,
  },
  headerTitle: {
    ...componentTokens.text.heading,
    color: designTokens.colors.semantic.text,
  },
  searchCard: {
    borderRadius: designTokens.borderRadius.components.input * 3, // More rounded for search
    overflow: 'hidden',
    ...designTokens.shadows.sm,
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: designTokens.spacing.scale.md,
    paddingVertical: designTokens.spacing.components.button.paddingVertical,
    gap: designTokens.spacing.scale.sm,
  },
  searchInput: {
    flex: 1,
    ...designTokens.typography.styles.body,
    color: designTokens.colors.semantic.text,
  },
  content: {
    flex: 1,
  },
  scrollContent: {
    paddingHorizontal: designTokens.spacing.scale.lg,
    paddingBottom: designTokens.spacing.scale.lg,
    gap: designTokens.spacing.components.list.itemSpacing,
  },
  conversationItem: {
    marginBottom: designTokens.spacing.scale.xs,
  },
  conversationCard: {
    ...componentTokens.card.default,
    padding: designTokens.spacing.scale.md,
    overflow: 'hidden',
  },
  conversationContent: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: designTokens.spacing.scale.sm,
  },
  avatarContainer: {
    position: 'relative',
  },
  unreadBadge: {
    position: 'absolute',
    top: -designTokens.spacing.scale.xs,
    right: -designTokens.spacing.scale.xs,
    backgroundColor: designTokens.colors.semantic.accent, // Coral accent for notifications
    borderRadius: designTokens.borderRadius.full,
    minWidth: 20,
    height: 20,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: designTokens.colors.semantic.surface,
    ...designTokens.shadows.sm,
  },
  unreadCount: {
    ...designTokens.typography.styles.caption,
    color: designTokens.colors.components.button.text,
    fontSize: 11,
    fontWeight: '600',
  },
  messageContent: {
    flex: 1,
    gap: designTokens.spacing.scale.xs,
  },
  messageHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  companionName: {
    ...componentTokens.text.body,
    fontWeight: '600',
    color: designTokens.colors.semantic.text,
  },
  timestamp: {
    ...componentTokens.text.caption,
    color: designTokens.colors.semantic.textSecondary,
  },
  messagePreview: {
    minHeight: 20,
    justifyContent: 'center',
  },
  lastMessage: {
    ...componentTokens.text.caption,
    color: designTokens.colors.semantic.textSecondary,
  },
  unreadMessage: {
    color: designTokens.colors.semantic.text,
    fontWeight: '500',
  },
  typingIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: designTokens.spacing.scale.xs,
  },
  typingDots: {
    flexDirection: 'row',
    gap: 2,
  },
  typingDot: {
    width: 4,
    height: 4,
    borderRadius: 2,
    backgroundColor: designTokens.colors.semantic.accent, // Coral accent for typing indicator
  },
  typingDot1: {
    opacity: 0.4,
  },
  typingDot2: {
    opacity: 0.7,
  },
  typingDot3: {
    opacity: 1,
  },
  typingText: {
    ...componentTokens.text.caption,
    color: designTokens.colors.semantic.accent,
    fontStyle: 'italic',
  },
  conversationActions: {
    flexDirection: 'row',
    gap: designTokens.spacing.scale.xs,
  },
  actionButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: `${designTokens.colors.semantic.accent}15`, // Coral accent with transparency
    justifyContent: 'center',
    alignItems: 'center',
    ...designTokens.shadows.sm,
  },
  emptyState: {
    ...componentTokens.card.default,
    alignItems: 'center',
    marginTop: designTokens.spacing.scale['2xl'],
    padding: designTokens.spacing.scale['2xl'],
    gap: designTokens.spacing.scale.sm,
  },
  emptyStateTitle: {
    ...componentTokens.text.subheading,
    color: designTokens.colors.semantic.text,
    textAlign: 'center',
  },
  emptyStateText: {
    ...componentTokens.text.caption,
    color: designTokens.colors.semantic.textSecondary,
    textAlign: 'center',
    lineHeight: designTokens.typography.lineHeights.relaxed * designTokens.typography.sizes.caption,
  },
});