import { StyleSheet, Text, View, Switch, SafeAreaView, TouchableOpacity } from "react-native";
import React, { useState, useEffect } from "react";
import { designTokens } from "@/constants/design-tokens";
import { RadialGradient } from "@/components/ui/RadialGradient";
import { router } from "expo-router";
import { ArrowLeft } from "lucide-react-native";
import { useCustomerProfile, useUpdateCustomerProfile } from '@/app/api/customer/customerProfile';
import { useQueryClient } from "@tanstack/react-query";

const NotificationSettings = () => {
  const { data, isLoading, error } = useCustomerProfile() as any;
  const updateProfile = useUpdateCustomerProfile() as any;
  const queryClient = useQueryClient();
  
  const [push, setPush] = useState(false);
  const [email, setEmail] = useState(false);
  const [sms, setSms] = useState(false);

  useEffect(() => {
    setPush(!!data?.data?.preferences?.notifications?.push);
    setEmail(!!data?.data?.preferences?.notifications?.email);
    setSms(!!data?.data?.preferences?.notifications?.sms);
  }, [data]);

  return (
    <RadialGradient variant="appBackground" style={styles.container}>
      <SafeAreaView style={{flex: 1}}>
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => router.back()}
        >
          <ArrowLeft size={24} color={designTokens.colors.semantic.primary} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Notification Settings</Text>
       <View style={{width: 40}} />
      </View>
        <View style={styles.settingRow}>
          <Text style={styles.label}>Push Notifications</Text>
          <Switch
            value={push}
            onValueChange={(val) => {
              setPush(val);
              updateProfile.mutate(
                { preferences: { notifications: { ...data?.data?.preferences?.notifications, push: val } } },
                {
                  onError: () => setPush(!val),
                  onSuccess: () => {
                    queryClient.invalidateQueries({ queryKey: ['customerProfile'] });
                  },
                }
              );
            }}
            trackColor={{
              false: designTokens.colors.semantic.border,
              true: designTokens.colors.semantic.primary,
            }}
            thumbColor={push
              ? designTokens.colors.semantic.background
              : designTokens.colors.semantic.surface}
          />
        </View>
        <View style={styles.settingRow}>
          <Text style={styles.label}>Email Notifications</Text>
          <Switch
            value={email}
            onValueChange={(val) => {
              setEmail(val);
              updateProfile.mutate(
                { preferences: { notifications: { ...data?.data?.preferences?.notifications, email: val } } },
                {
                  onError: () => setEmail(!val),
                  onSuccess: () => {
                    queryClient.invalidateQueries({ queryKey: ['customerProfile'] });
                  },
                }
              );
            }}
            trackColor={{
              false: designTokens.colors.semantic.border,
              true: designTokens.colors.semantic.primary,
            }}
            thumbColor={email
              ? designTokens.colors.semantic.background
              : designTokens.colors.semantic.surface}
          />
        </View>
        <View style={styles.settingRow}>
          <Text style={styles.label}>SMS Notifications</Text>
          <Switch
            value={sms}
            onValueChange={(val) => {
              setSms(val);
              updateProfile.mutate(
                { preferences: { notifications: { ...data?.data?.preferences?.notifications, sms: val } } },
                {
                  onError: () => setSms(!val),
                  onSuccess: () => {
                    queryClient.invalidateQueries({ queryKey: ['customerProfile'] });
                  },
                }
              );
            }}
            trackColor={{
              false: designTokens.colors.semantic.border,
              true: designTokens.colors.semantic.primary,
            }}
            thumbColor={sms
              ? designTokens.colors.semantic.background
              : designTokens.colors.semantic.surface}
          />
        </View>
      </SafeAreaView>
    </RadialGradient>
  );
};

export default NotificationSettings;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: designTokens.colors.semantic.background,
    padding: 24,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',

    paddingBottom: 30,
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: designTokens.colors.semantic.surface,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: designTokens.colors.semantic.text,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: designTokens.colors.semantic.text,
  },  
  title: {
    fontSize: 22,
    fontWeight: "700",
    marginBottom: 32,
    color: designTokens.colors.semantic.text,
  },
  settingRow: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    marginBottom: 24,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: designTokens.colors.semantic.border,
  },
  label: {
    fontSize: 16,
    color: designTokens.colors.semantic.text,
  },
});
