import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
  Modal,
  SafeAreaView,
  Platform,
} from 'react-native';
import { router } from 'expo-router';
import { useCustomerProfile, useUpdateCustomerProfile } from '../../api/customer/customerProfile';

import { RadialGradient } from '@/components/ui/RadialGradient';
import { ProfileImage } from '@/components/ui/ProfileImage';
import { Input } from '@/components/ui/Input';
import { Button } from '@/components/ui/Button';
import { Card } from '@/components/ui/Card';
import {
  ArrowLeft,
  Camera,
  Save,
  User,
  Phone,
  Mail,
  MapPin,
  Calendar,
} from 'lucide-react-native';
import designTokens from '@/constants/design-tokens';
import { SimpleInput } from '@/components/ui/SimpleInput';
import DateTimePicker from '@react-native-community/datetimepicker';
import { useQueryClient } from '@tanstack/react-query';

export default function ProfileEditScreen() {
  const { data, isLoading, error } = useCustomerProfile() as any;
  const updateProfile = useUpdateCustomerProfile() as any;
  const queryClient = useQueryClient();
  // console.log(data);

  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    bio: '',
    dateOfBirth: '',
  });
  const [profileImage, setProfileImage] = useState<string>(
    'https://images.unsplash.com/photo-1534528741775-53994a69daeb?q=80&w=1000&auto=format&fit=crop'
  );
  const [showDatePicker, setShowDatePicker] = useState(false);
  const [tempDate, setTempDate] = useState<Date | null>(null);

  React.useEffect(() => {
    if ((data as any)?.data) {
      setFormData({
        name: (data as any).data.name || '',
        email: (data as any).data.email || '',
        phone: (data as any).data.phone || '',
        bio: (data as any).data.bio || '',
        dateOfBirth: (data as any).data.dateOfBirth || '',
      });
      if ((data as any).data.profileImage) setProfileImage((data as any).data.profileImage);
    }
  }, [data]);

  const handleSave = () => {
    updateProfile.mutate(
      {
        ...formData,
        profileImage,
      } as any,
      {
        onSuccess: () => {
          Alert.alert(
            'Profile Updated',
            'Your profile has been successfully updated.',
            [
              {
                text: 'OK',
                onPress: () => {
                  router.back();
                  queryClient.invalidateQueries({ queryKey: ['customerProfile'] });
                },
              },
            ]
          );
        },
        onError: (err: any) => {
          Alert.alert('Update Failed', err?.message || 'An error occurred');
        },
      }
    );
  };

  const handleImageEdit = () => {
    Alert.alert(
      'Change Profile Picture',
      'Choose an option',
      [
        {
          text: 'Camera',
          onPress: () => {
            // Implement camera functionality
            // console.log('Open camera');
          },
        },
        {
          text: 'Gallery',
          onPress: () => {
            // Implement gallery functionality
            // console.log('Open gallery');
          },
        },
        {
          text: 'Cancel',
          style: 'cancel',
        },
      ]
    );
  };

  const handleDateChange = (event: any, selectedDate?: Date) => {
    // Always update tempDate, don't close modal
    if (selectedDate) {
      setTempDate(selectedDate);
    }
  };

  const handleDatePickerOpen = () => {
    setTempDate(formData.dateOfBirth ? new Date(formData.dateOfBirth) : new Date());
    setShowDatePicker(true);
  };

  const handleDatePickerDone = () => {
    if (tempDate) {
      setFormData({
        ...formData,
        dateOfBirth: tempDate.toISOString().split('T')[0],
      });
    }
    setShowDatePicker(false);
  };

  if (isLoading) {
    return (
      <SafeAreaView style={styles.loadingContainer}>
        <Text style={styles.loadingText}>Loading profile...</Text>
      </SafeAreaView>
    );
  }

  if (error) {
    return (
      <RadialGradient variant="appBackground" style={styles.container}>
        <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}>
          <Text>Error loading profile</Text>
          <Text>{error?.message || 'Unknown error'}</Text>
        </View>
      </RadialGradient>
    );
  }

  return (
    <RadialGradient variant="appBackground" style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => router.back()}
        >
          <ArrowLeft size={24} color={designTokens.colors.semantic.primary} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Edit Profile</Text>
        <TouchableOpacity
          style={styles.saveButton}
          onPress={handleSave}
        >
          <Save size={24} color={designTokens.colors.semantic.primary} />
        </TouchableOpacity>
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        <Card style={styles.profileCard}>
          <View style={styles.profileImageSection}>
            <ProfileImage
              uri={profileImage}
              size={120}
              editable={true}
              onEdit={handleImageEdit}
            />
            <Text style={styles.changePhotoText}>Tap to change photo</Text>
          </View>
        </Card>

        <Card style={styles.formCard}>
          <Text style={styles.sectionTitle}>Personal Information</Text>
          
          <SimpleInput
            label="Full Name"
            value={formData.name}
            onChangeText={(text: string) => setFormData({ ...formData, name: text })}
            placeholder="Enter your full name"
          />

          <SimpleInput
            label="Email"
            value={formData.email}
            onChangeText={(text) => setFormData({ ...formData, email: text })}
            placeholder="Enter your email"
            keyboardType="email-address"
            disabled={true}
          />

          <SimpleInput
            label="Phone Number"
            value={formData.phone}
            onChangeText={(text) => setFormData({ ...formData, phone: text })}
            placeholder="Enter your phone number"
            keyboardType="phone-pad"
          />

          <SimpleInput
            label="Date of Birth"
            value={formData.dateOfBirth}
            placeholder="YYYY-MM-DD"
            editable={false}
            onPressIn={handleDatePickerOpen}
          />
          {Platform.OS === 'ios' ? (
            <Modal
              visible={showDatePicker}
              transparent
              animationType="slide"
              onRequestClose={() => setShowDatePicker(false)}
            >
              <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center', backgroundColor: 'rgba(0,0,0,0.2)' }}>
                <View style={{ backgroundColor: '#fff', borderRadius: 16, padding: 16, alignItems: 'center' }}>
                  <DateTimePicker
                    value={tempDate || new Date()}
                    mode="date"
                    display="spinner"
                    onChange={handleDateChange}
                    maximumDate={new Date()}
                    style={{ width: 250 }}
                  />
                  <TouchableOpacity onPress={handleDatePickerDone} style={{ marginTop: 12, paddingHorizontal: 24, paddingVertical: 10, backgroundColor: designTokens.colors.semantic.primary, borderRadius: 8 }}>
                    <Text style={{ color: '#fff', fontWeight: '600', fontSize: 16 }}>Done</Text>
                  </TouchableOpacity>
                  <TouchableOpacity onPress={() => setShowDatePicker(false)} style={{ marginTop: 8 }}>
                    <Text style={{ color: designTokens.colors.semantic.error, fontSize: 14 }}>Cancel</Text>
                  </TouchableOpacity>
                </View>
              </View>
            </Modal>
          ) : (
            showDatePicker && (
              <DateTimePicker
                value={tempDate || new Date()}
                mode="date"
                display="default"
                onChange={(event, selectedDate) => {
                  setShowDatePicker(false);
                  if (selectedDate) {
                    setFormData({
                      ...formData,
                      dateOfBirth: selectedDate.toISOString().split('T')[0],
                    });
                  }
                }}
                maximumDate={new Date()}
              />
            )
          )}

          <SimpleInput
            label="Bio"
            value={formData.bio}
            onChangeText={(text) => setFormData({ ...formData, bio: text })}
            placeholder="Tell us about yourself"
            multiline
            numberOfLines={4}
            style={styles.bioInput}
          />
        </Card>

        <View style={styles.buttonContainer}>
          <Button
            title="Save Changes"
            onPress={handleSave}
            variant="primary"
            style={styles.saveButtonLarge}
          />
          
          <Button
            title="Cancel"
            onPress={() => router.back()}
            variant="outline"
            style={styles.cancelButton}
          />
        </View>
      </ScrollView>
    </RadialGradient>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingTop: 60,
    paddingBottom: 20,
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: designTokens.colors.semantic.surface,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: designTokens.colors.semantic.text,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: designTokens.colors.semantic.text,
  },
  saveButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: designTokens.colors.semantic.surface,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: designTokens.colors.semantic.text,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  content: {
    flex: 1,
    paddingHorizontal: 20,
  },
  profileCard: {
    marginBottom: 20,
    paddingVertical: 30,
  },
  profileImageSection: {
    alignItems: 'center',
  },
  changePhotoText: {
    marginTop: 12,
    fontSize: 14,
    color: designTokens.colors.semantic.textSecondary,
    fontWeight: '500',
  },
  formCard: {
    marginBottom: 20,
    padding: 20,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: designTokens.colors.semantic.text,
    marginBottom: 20,
  },
  bioInput: {
    minHeight: 100,
    textAlignVertical: 'top',
    borderWidth: 1,
    borderColor: designTokens.colors.semantic.border,
    borderRadius: designTokens.borderRadius.lg,
    padding: designTokens.spacing.scale.md,
  },
  buttonContainer: {
    paddingBottom: 40,
  },
  saveButtonLarge: {
    marginBottom: 12,
  },
  cancelButton: {
    marginBottom: 20,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    color: designTokens.colors.semantic.text,
  },
});

