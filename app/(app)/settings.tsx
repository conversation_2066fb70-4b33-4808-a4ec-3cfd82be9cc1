import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
} from 'react-native';
import { router } from 'expo-router';
import { useAuthStore } from '@/stores/auth-store';
import { designTokens } from '@/constants/design-tokens';
import { RadialGradient } from '@/components/ui/RadialGradient';
import { Card } from '@/components/ui/Card';
import {
  User,
  Bell,
  Shield,
  HelpCircle,
  LogOut,
  ChevronRight,
  Moon,
  Globe,
  Star,
} from 'lucide-react-native';

interface SettingsItemProps {
  icon: React.ReactNode;
  title: string;
  subtitle?: string;
  onPress: () => void;
  showChevron?: boolean;
  danger?: boolean;
}

const SettingsItem: React.FC<SettingsItemProps> = ({
  icon,
  title,
  subtitle,
  onPress,
  showChevron = true,
  danger = false,
}) => (
  <TouchableOpacity style={styles.settingsItem} onPress={onPress}>
    <View style={styles.settingsItemLeft}>
      <View style={[styles.iconContainer, danger && styles.dangerIconContainer]}>
        {icon}
      </View>
      <View style={styles.textContainer}>
        <Text style={[styles.settingsTitle, danger && styles.dangerText]}>
          {title}
        </Text>
        {subtitle && (
          <Text style={styles.settingsSubtitle}>{subtitle}</Text>
        )}
      </View>
    </View>
    {showChevron && (
      <ChevronRight size={20} color={designTokens.colors.semantic.textSecondary} />
    )}
  </TouchableOpacity>
);

export default function SettingsScreen() {
  const { user, logout } = useAuthStore();

  const handleLogout = () => {
    Alert.alert(
      'Logout',
      'Are you sure you want to logout?',
      [
        {
          text: 'Cancel',
          style: 'cancel',
        },
        {
          text: 'Logout',
          style: 'destructive',
          onPress: () => {
            logout();
            router.replace('/splash');
          },
        },
      ]
    );
  };

  return (
    <RadialGradient variant="appBackground" style={styles.container}>
      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Profile Section */}
        <Card style={styles.profileCard}>
          <View style={styles.profileHeader}>
            <Text style={styles.sectionTitle}>Account</Text>
          </View>
          
          <SettingsItem
            icon={<User size={22} color={designTokens.colors.semantic.primary} />}
            title="Edit Profile"
            subtitle="Update your personal information"
            onPress={() => router.push('/profile/edit')}
          />
        </Card>

        {/* App Settings */}
        <Card style={styles.settingsCard}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>App Settings</Text>
          </View>
          
          <SettingsItem
            icon={<Bell size={22} color={designTokens.colors.semantic.primary} />}
            title="Notifications"
            subtitle="Manage your notification preferences"
            onPress={() =>  router.push('/(app)/profile/notificationSettings')}
          />
          
        
          
          <SettingsItem
            icon={<Globe size={22} color={designTokens.colors.semantic.primary} />}
            title="Language"
            subtitle="English"
            onPress={() => {
              Alert.alert('Coming Soon', 'Language settings will be available in a future update.');
            }}
          />
        </Card>

        {/* Privacy & Security */}
        <Card style={styles.settingsCard}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>Privacy & Security</Text>
          </View>
          
          <SettingsItem
            icon={<Shield size={22} color={designTokens.colors.semantic.primary} />}
            title="Privacy Policy"
            subtitle="Read our privacy policy"
            onPress={() => {
              Alert.alert('Coming Soon', 'Privacy policy will be available in a future update.');
            }}
          />
        </Card>

        {/* Support */}
        <Card style={styles.settingsCard}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>Support</Text>
          </View>
          
          <SettingsItem
            icon={<HelpCircle size={22} color={designTokens.colors.semantic.primary} />}
            title="Help Center"
            subtitle="Get help and support"
            onPress={() => {
              Alert.alert('Coming Soon', 'Help center will be available in a future update.');
            }}
          />
          
          <SettingsItem
            icon={<Star size={22} color={designTokens.colors.semantic.primary} />}
            title="Rate App"
            subtitle="Rate us on the App Store"
            onPress={() => {
              Alert.alert('Thank You!', 'We appreciate your feedback.');
            }}
          />
        </Card>

        {/* Logout */}
        <Card style={styles.settingsCard}>
          <SettingsItem
            icon={<LogOut size={22} color={designTokens.colors.semantic.error} />}
            title="Logout"
            subtitle="Sign out of your account"
            onPress={handleLogout}
            showChevron={false}
            danger={true}
          />
        </Card>

        <View style={styles.footer}>
          <Text style={styles.versionText}>Version 1.0.0</Text>
          <Text style={styles.copyrightText}>© 2024 Tirak. All rights reserved.</Text>
        </View>
      </ScrollView>
    </RadialGradient>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    flex: 1,
    paddingHorizontal: 20,
    paddingTop: 20,
  },
  profileCard: {
    marginBottom: 20,
    padding: 20,
  },
  profileHeader: {
    marginBottom: 16,
  },
  settingsCard: {
    marginBottom: 20,
    padding: 20,
  },
  sectionHeader: {
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: designTokens.colors.semantic.text,
  },
  settingsItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: designTokens.colors.semantic.border,
  },
  settingsItemLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  iconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: designTokens.colors.semantic.surface + '80',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  dangerIconContainer: {
    backgroundColor: designTokens.colors.semantic.error + '20',
  },
  textContainer: {
    flex: 1,
  },
  settingsTitle: {
    fontSize: 16,
    fontWeight: '500',
    color: designTokens.colors.semantic.text,
    marginBottom: 2,
  },
  dangerText: {
    color: designTokens.colors.semantic.error,
  },
  settingsSubtitle: {
    fontSize: 14,
    color: designTokens.colors.semantic.textSecondary,
  },
  footer: {
    alignItems: 'center',
    paddingVertical: 40,
  },
  versionText: {
    fontSize: 14,
    color: designTokens.colors.semantic.textSecondary,
    marginBottom: 4,
  },
  copyrightText: {
    fontSize: 12,
    color: designTokens.colors.semantic.textSecondary,
  },
});
