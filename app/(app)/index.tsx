import React, { useMemo } from 'react';
import { View, StyleSheet, ScrollView, ActivityIndicator } from 'react-native';
import { router } from 'expo-router';
import { useAuthStoreHydrated } from '@/hooks/useAuthStoreHydrated';
import { RadialGradient } from '@/components/ui/RadialGradient';
import { SearchHeader } from '@/components/home/<USER>';
import { CategorySection } from '@/components/home/<USER>';
import { FeaturedCompanionsSection } from '@/components/home/<USER>';
// import { PopularExperiencesSection } from '@/components/home/<USER>';
import { CompanionDashboard } from '@/components/home/<USER>';
import { designTokens } from '@/constants/design-tokens';
import { useFeaturedCompanions, Companion as APICompanion } from '@/app/api/companion/companion';
import { Companion } from '@/types/companion';

// Helper function to transform API companion data to match existing interface
const transformAPICompanion = (apiCompanion: APICompanion): Companion => {
  return {
    id: apiCompanion.id,
    name: apiCompanion.name,
    age: apiCompanion.age || 25, // Default age if not provided
    location: apiCompanion.location,
    rating: apiCompanion.rating,
    reviews: apiCompanion.reviewCount,
    price: apiCompanion.price,
    image: apiCompanion.profileImage,
    services: apiCompanion.services,
    languages: apiCompanion.languages,
    verified: apiCompanion.verified,
    online: apiCompanion.online,
    category: apiCompanion.categories[0] || 'general', // Use first category as main category
    bio: apiCompanion.bio,
    gallery: apiCompanion.gallery,
  };
};

// TypeScript interfaces

interface PopularExperience {
  id: string;
  title: string;
  category: string;
  price: number;
  duration: string;
  rating: number;
  image: string;
  location: string;
}

type Category = {
  id: string;
  name: string;
  icon: string;
  color: string;
  blob?: any;
};

// Main categories (displayed in single row) - Enhanced with unique icons
const mainCategories = [
  { id: 'travel', name: 'Travel Partner', icon: '✈️', color: designTokens.colors.semantic.primary },
  { id: 'nightlife', name: 'Nightlife', icon: '🍸', color: designTokens.colors.semantic.warning },
  { id: 'cinema', name: 'Cinema', icon: '🎬', color: designTokens.colors.semantic.accent },
] as any;

// All categories (for dropdown) - Enhanced with unique, culturally appropriate icons
const allCategories = [
  { id: 'travel', name: 'Travel Partner', icon: '✈️', color: designTokens.colors.semantic.primary },
  { id: 'nightlife', name: 'Nightlife', icon: '🍸', color: designTokens.colors.semantic.warning },
  { id: 'cinema', name: 'Cinema', icon: '🎬', color: designTokens.colors.semantic.accent },
  { id: 'holiday', name: 'Holiday', icon: '🏖️', color: designTokens.colors.semantic.secondary },
  { id: 'wellness', name: 'Wellness', icon: '🧖', color: designTokens.colors.semantic.success },
  { id: 'explorer', name: 'Explorer', icon: '🏙️', color: designTokens.colors.semantic.primary },
  { id: 'private', name: 'Private', icon: '🔒', color: designTokens.colors.semantic.textSecondary },
  { id: 'events', name: 'Events', icon: '🎉', color: designTokens.colors.semantic.error },
  { id: 'sports', name: 'Sports', icon: '🏋️', color: designTokens.colors.semantic.surface },
] as any;

const popularExperiences: PopularExperience[] = [
  {
    id: '1',
    title: 'Bangkok Night Tour',
    price: 2500,
    duration: '4 hours',
    image: 'https://images.unsplash.com/photo-1508009603885-50cf7c8dd0d5?q=80&w=1000&auto=format&fit=crop',
    rating: 4.8,
    category: 'Nightlife',
    location: 'Bangkok',
  },
  {
    id: '2',
    title: 'Phuket Island Hopping',
    price: 3500,
    duration: '8 hours',
    image: 'https://images.unsplash.com/photo-1537956965359-7573183d1f57?q=80&w=1000&auto=format&fit=crop',
    rating: 4.9,
    category: 'Travel',
    location: 'Phuket',
  },
  {
    id: '3',
    title: 'Chiang Mai Temple Tour',
    price: 1800,
    duration: '6 hours',
    image: 'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?q=80&w=1000&auto=format&fit=crop',
    rating: 4.7,
    category: 'Culture',
    location: 'Chiang Mai',
  },
];

/**
 * HomeScreen Component
 *
 * Comprehensive home screen with component-based architecture following
 * the established design system and brand theme.
 *
 * Features:
 * - TypeScript interfaces for type safety
 * - Component-based architecture for reusability
 * - Design token implementation throughout
 * - Coral accent integration
 * - Organic motion design philosophy
 * - Responsive layout with proper error handling
 */
export default function HomeScreen() {
  // Call all hooks first, unconditionally
  const { user, isHydrated } = useAuthStoreHydrated();
  const { data: featuredCompanionsData, isLoading: companionsLoading, error: companionsError } = useFeaturedCompanions();

  // Derived state using useMemo
  const isCompanion = useMemo(() => {
    return user?.userType === 'companion' || user?.userType === 'supplier';
  }, [user?.userType]);

  const transformedCompanions = useMemo(() => {
    if (!featuredCompanionsData?.data?.companions) return [];
    return featuredCompanionsData.data.companions.map(transformAPICompanion);
  }, [featuredCompanionsData]);

  // Navigation handlers with error handling
  const handleSeeAllCompanions = React.useCallback(() => {
    try {
      router.push('/search');
    } catch (error) {
      console.error('Navigation error:', error);
    }
  }, []);

  const handleSeeAllExperiences = React.useCallback(() => {
    try {
      router.push('/search?type=experiences');
    } catch (error) {
      console.error('Navigation error:', error);
    }
  }, []);

  // Render loading state
  if (!isHydrated) {
    return (
      <RadialGradient variant="appBackground" style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={designTokens.colors.semantic.primary} />
      </RadialGradient>
    );
  }

  // Render content based on user type
  const content = isCompanion && user ? (
    <ScrollView
      showsVerticalScrollIndicator={false}
      contentContainerStyle={styles.scrollContent}
    >
      <SearchHeader userName={user.name} isCompanion={true} />
      <CompanionDashboard userName={user.name} />
    </ScrollView>
  ) : (
    <ScrollView
      showsVerticalScrollIndicator={false}
      contentContainerStyle={styles.scrollContent}
    >
      <SearchHeader userName={user?.name} isCompanion={false} />
      <View style={styles.content}>
        <CategorySection
          mainCategories={mainCategories}
          allCategories={allCategories}
        />
        <FeaturedCompanionsSection
          companions={transformedCompanions}
          onSeeAll={handleSeeAllCompanions}
          loading={companionsLoading}
          error={companionsError}
        />
      </View>
    </ScrollView>
  );

  return (
    <RadialGradient variant="appBackground" style={styles.container}>
      {content}
    </RadialGradient>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  scrollContent: {
    flexGrow: 1,
    paddingBottom: designTokens.spacing.scale.xl,
  },
  content: {
    paddingHorizontal: designTokens.spacing.scale.lg,
    gap: designTokens.spacing.scale.lg,
  },
});