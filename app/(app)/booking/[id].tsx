import React from 'react';
import { View, Text, StyleSheet, ActivityIndicator, ScrollView, SafeAreaView } from 'react-native';
import { useLocalSearchParams, useRouter } from 'expo-router';
import { RadialGradient } from '@/components/ui/RadialGradient';
import { Card } from '@/components/ui/Card';
import { Heading, Subheading, Body, Caption } from '@/components/ui/Typography';
import { designTokens } from '@/constants/design-tokens';
import { useBookingQuery } from '@/app/api/booking/booking';
import { ProfileImage } from '@/components/ui/ProfileImage';
import { Button } from '@/components/ui/Button';
import { useAuthStore } from '@/stores/auth-store';

const BookingDetailsScreen = () => {
  const { user, isAuthenticated } = useAuthStore();
  const isCompanion = user?.userType === 'companion' || user?.userType === 'supplier';
  const { id } = useLocalSearchParams<{ id: string }>();
  const router = useRouter();
  const { data, isLoading, error } = useBookingQuery(id);

  if (isLoading) {
    return (
      <RadialGradient variant="appBackground" style={styles.container}>
        <SafeAreaView style={{flex: 1, justifyContent: 'center', alignItems: 'center'}}>
        <ActivityIndicator size="large" color={designTokens.colors.semantic.primary} />
        <Text style={styles.loadingText}>Loading booking details...</Text>
        </SafeAreaView>
      </RadialGradient>
    );
  }

  if (error || !data?.success) {
    return (
      <RadialGradient variant="appBackground" style={styles.container}>
        <Text style={styles.errorText}>Failed to load booking details.</Text>
        <Button title="Go Back" onPress={() => router.back()} />
      </RadialGradient>
    );
  }

  const booking = data.data.booking;
  const customer = booking.customer;
  
  // Use service or experience if available
  const service = booking.service ? booking.service : (booking as any).experience;
  const companion = booking.companion;
  const experience = (booking as any).experience;
  const paymentMethod = booking.paymentMethod;
  const timeline = booking.timeline || [];
  const customerPreferences = (booking as any).customerPreferences || {};

  return (
    <RadialGradient variant="appBackground" style={styles.container}>
      <ScrollView contentContainerStyle={styles.scrollContent}>
        <Card style={styles.card}>
          {/* Customer & Companion Info */}
          <Subheading>{!isCompanion ? 'Companion' : 'Customer'}</Subheading>
          <View style={styles.headerRow}>
            <ProfileImage uri={!isCompanion && companion?.profileImage || customer?.profileImage || ''} size={64} />
            <View style={styles.headerText}>
              <Heading>{!isCompanion && companion?.name || customer?.name || 'Unknown Companion'}</Heading>
              <Caption>{!isCompanion && companion?.phone || customer?.phone || ''}</Caption>
            </View>
          </View>
          {/* <Subheading>Companion</Subheading>
          <View style={styles.headerRow}>
            <ProfileImage uri={companion?.profileImage || ''} size={64} />
            <View style={styles.headerText}>
              <Heading>{companion?.name || 'Unknown Companion'}</Heading>
              <Caption>{companion?.phone || ''}</Caption>
            </View>
          </View> */}

          {/* Status & Service/Experience */}
          <View style={styles.section}>
            <Subheading>Status</Subheading>
            <Body>{booking.status.charAt(0).toUpperCase() + booking.status.slice(1)}</Body>
          </View>
          <View style={styles.section}>
            <Subheading>Service / Experience</Subheading>
            <Body>{service?.name || experience?.name || 'N/A'}</Body>
          </View>

          {/* Date, Time, Location, Meeting Point */}
          <View style={styles.section}>
            <Subheading>Booking Date</Subheading>
            <Body>{booking.date.split('T')[0]}</Body>
          </View>
          <View style={styles.section}>
            <Subheading>Booking Time</Subheading>
            <Body>{booking.startTime} - {booking.endTime} ({booking.duration}Mins)</Body>
          </View>
          <View style={styles.section}>
            <Subheading>Location</Subheading>
            <Body>{booking.location || 'N/A'}</Body>
          </View>
          <View style={styles.section}>
            <Subheading>Meeting Point</Subheading>
            <Body>{booking.meetingPoint || 'N/A'}</Body>
          </View>

          {/* Payment & Fees */}
          <View style={styles.section}>
            <Subheading>Payment Status</Subheading>
            <Body>{booking.paymentStatus.charAt(0).toUpperCase() + booking.paymentStatus.slice(1)}</Body>
          </View>
          
          <View style={styles.section}>
            <Subheading>Service Fee</Subheading>
            <Body>฿{booking.serviceFee?.toLocaleString() || 'N/A'}</Body>
          </View>
          <View style={styles.section}>
            <Subheading>Total Amount</Subheading>
            <Body>฿{booking.totalAmount?.toLocaleString() || 'N/A'}</Body>
          </View>

          {/* Preferences & Special Requests */}
          <View style={styles.section}>
            <Subheading>Special Requests</Subheading>
            <Body>{booking.specialRequests || 'N/A'}</Body>
          </View>
          <View style={styles.section}>
            <Subheading>Preferred Languages</Subheading>
            <Body>{Array.isArray((booking as any).preferredLanguages) && (booking as any).preferredLanguages.length > 0 ? (booking as any).preferredLanguages.join(', ') : 'N/A'}</Body>
          </View>
          <View style={styles.section}>
            <Subheading>Dietary Restrictions</Subheading>
            <Body>{Array.isArray((booking as any).dietaryRestrictions) && (booking as any).dietaryRestrictions.length > 0 ? (booking as any).dietaryRestrictions.join(', ') : 'N/A'}</Body>
          </View>
          <View style={styles.section}>
            <Subheading>Accessibility Needs</Subheading>
            <Body>{Array.isArray((booking as any).accessibilityNeeds) && (booking as any).accessibilityNeeds.length > 0 ? (booking as any).accessibilityNeeds.join(', ') : 'N/A'}</Body>
          </View>

          {/* Timeline */}
          <View style={styles.section}>
            <Subheading>Timeline</Subheading>
            {Array.isArray(timeline) && timeline.length > 0 ? (
              timeline.map((item: any, idx: number) => (
                <Body key={idx}>{item.status.charAt(0).toUpperCase() + item.status.slice(1)}: {item.timestamp.split('T')[1].split(':').slice(0, 2).join(':')}</Body>
              ))
            ) : (
              <Body>N/A</Body>
            )}
          </View>
        </Card>
      </ScrollView>
    </RadialGradient>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  loadingText: {
    marginTop: 16,
    color: designTokens.colors.semantic.primary,
    fontSize: 16,
    fontWeight: '500',
  },
  errorText: {
    color: designTokens.colors.semantic.error,
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 16,
  },
  scrollContent: {
    padding: 24,
  },
  card: {
    padding: 20,
    borderRadius: 16,
    backgroundColor: designTokens.colors.semantic.surface,
    shadowColor: '#000',
    shadowOpacity: 0.08,
    shadowRadius: 8,
    elevation: 2,
  },
  headerRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 20,
  },
  headerText: {
    marginLeft: 16,
  },
  section: {
    marginBottom: 16,
  },
});

export default BookingDetailsScreen; 