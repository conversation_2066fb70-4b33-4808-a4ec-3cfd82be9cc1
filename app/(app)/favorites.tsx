import React, { useState } from 'react';
import { View, Text, StyleSheet, FlatList, TouchableOpacity, TextInput, ActivityIndicator } from 'react-native';
import { router } from 'expo-router';
import { RadialGradient } from '@/components/ui/RadialGradient';
import { Card } from '@/components/ui/Card';
import { CompanionCard } from '@/components/ui/CompanionCard';
import { designTokens } from '@/constants/design-tokens';
import { Heart, Search, Filter, Grid3X3, List } from 'lucide-react-native';
import { useFavoritesStore } from '@/stores/favorites-store';
import { useQueries } from '@tanstack/react-query';
import { fetchCompanionById } from '@/app/api/companion/companion';

export default function FavoritesScreen() {
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const favoriteIds = useFavoritesStore((s) => s.favorites);

  // Use useQueries to fetch all favorite companions by ID
  const favoriteQueries = useQueries({
    queries: favoriteIds.map((id) => ({
      queryKey: ['companion', id],
      queryFn: () => fetchCompanionById(id),
      enabled: !!id,
    })),
  });

  const isLoading = favoriteQueries.some((q) => q.isLoading);
  const favorites = favoriteQueries
    .map((q) => q.data?.data)
    .filter((companion): companion is NonNullable<typeof companion> => Boolean(companion))
    .map((companion) => ({
      ...companion,
      image: companion.profileImage || companion.image || 'https://ui-avatars.com/api/?name=Unknown&background=random',
      category: Array.isArray(companion.categories) && companion.categories.length > 0
        ? companion.categories[0]
        : 'general',
      reviews: Array.isArray(companion.reviews) ? companion.reviews.length : 0,
    }));
  
  const handleCompanionPress = (id: string) => {
    router.push(`/companion/${id}`);
  };
  
  const handleFavorite = (id: string) => {
    // No-op, handled in CompanionCard now
  };
  
  const handleMessage = (id: string) => {
    router.push(`/chat/${id}`);
  };
  
  const renderEmptyState = () => (
    <Card style={styles.emptyStateCard} padding={40}>
      <View style={styles.emptyStateIcon}>
        <Heart size={40} color={designTokens.colors.semantic.accent} />
      </View>
      <Text style={styles.emptyStateTitle}>No favorites yet</Text>
      <Text style={styles.emptyStateText}>
        When you find companions you like, tap the heart icon to add them to your favorites for easy access.
      </Text>
      <TouchableOpacity 
        style={styles.exploreButton}
        onPress={() => router.push('/search')}
      >
        <Text style={styles.exploreButtonText}>Explore Companions</Text>
      </TouchableOpacity>
    </Card>
  );
  
  if (isLoading) {
    return (
      <RadialGradient variant="appBackground" style={styles.container}>
        <View style={styles.header}>
          <Text style={styles.headerTitle}>Favorites</Text>
        </View>
        <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}>
          <ActivityIndicator size="large" color={designTokens.colors.semantic.primary} />
          <Text>Loading...</Text>
        </View>
      </RadialGradient>
    );
  }
  
  return (
    <RadialGradient variant="appBackground" style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.headerTitle}>Favorites</Text>
        
        <Card style={styles.searchCard} padding={0}>
          <View style={styles.searchContainer}>
            <View style={styles.searchBar}>
              <Search size={20} color={designTokens.colors.semantic.textSecondary} />
              <TextInput
                placeholder="Search favorites..."
                placeholderTextColor={designTokens.colors.semantic.textSecondary}
                style={styles.searchPlaceholder}
              />
            </View>
            <TouchableOpacity style={styles.filterButton}>
              <Filter size={20} color={designTokens.colors.semantic.primary} />
            </TouchableOpacity>
          </View>
        </Card>
      </View>
      
      {favorites.length > 0 ? (
        <>
          <Card style={styles.resultsHeader} padding={16}>
            <View style={styles.resultsHeaderContent}>
              <Text style={styles.resultsCount}>
                {favorites.length} favorites
              </Text>
              <View style={styles.viewToggle}>
                <TouchableOpacity
                  style={[styles.viewButton, viewMode === 'grid' && styles.viewButtonActive]}
                  onPress={() => setViewMode('grid')}
                >
                  <Grid3X3 size={18} color={viewMode === 'grid' ? designTokens.colors.semantic.surface : designTokens.colors.semantic.textSecondary} />
                </TouchableOpacity>
                <TouchableOpacity
                  style={[styles.viewButton, viewMode === 'list' && styles.viewButtonActive]}
                  onPress={() => setViewMode('list')}
                >
                  <List size={18} color={viewMode === 'list' ? designTokens.colors.semantic.surface : designTokens.colors.semantic.textSecondary} />
                </TouchableOpacity>
              </View>
            </View>
          </Card>
          
          <FlatList
            data={favorites}
            renderItem={({ item }) =>
              item ? (
              <CompanionCard
                companion={item}
                viewMode={viewMode}
                onPress={() => handleCompanionPress(item.id)}
                onFavorite={() => handleFavorite(item.id)}
                onMessage={() => handleMessage(item.id)}
              />
              ) : null
            }
            keyExtractor={(item) => item?.id}
            numColumns={viewMode === 'grid' ? 2 : 1}
            key={viewMode} // Force re-render when view mode changes
            contentContainerStyle={styles.listContainer}
            showsVerticalScrollIndicator={false}
          />
        </>
      ) : (
        renderEmptyState()
      )}
    </RadialGradient>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    paddingHorizontal: 20,
    paddingTop: 20,
    paddingBottom: 16,
    gap: 16,
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: designTokens.colors.semantic.text,
  },
  searchCard: {
    borderRadius: 25,
    overflow: 'hidden',
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  searchBar: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
  },
  searchPlaceholder: {
    color: designTokens.colors.semantic.textSecondary,
    marginLeft: 10,
    fontSize: 16,
  },
  filterButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(111, 76, 170, 0.1)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  resultsHeader: {
    marginHorizontal: 20,
    marginBottom: 16,
  },
  resultsHeaderContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  resultsCount: {
    fontSize: 16,
    fontWeight: '600',
    color: designTokens.colors.semantic.text,
  },
  viewToggle: {
    flexDirection: 'row',
    backgroundColor: designTokens.colors.semantic.border,
    borderRadius: 20,
    padding: 2,
  },
  viewButton: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 18,
  },
  viewButtonActive: {
    backgroundColor: designTokens.colors.semantic.primary,
  },
  listContainer: {
    paddingHorizontal: 20,
    paddingBottom: 20,
  },
  emptyStateCard: {
    margin: 20,
    alignItems: 'center',
  },
  emptyStateIcon: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: 'rgba(255, 121, 121, 0.1)',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 20,
  },
  emptyStateTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: designTokens.colors.semantic.text,
    marginBottom: 12,
  },
  emptyStateText: {
    fontSize: 16,
    color: designTokens.colors.semantic.textSecondary,
    textAlign: 'center',
    marginBottom: 24,
    lineHeight: 24,
  },
  exploreButton: {
    paddingVertical: 12,
    paddingHorizontal: 24,
    backgroundColor: designTokens.colors.semantic.primary,
    borderRadius: 25,
  },
  exploreButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: designTokens.colors.semantic.surface,
  },
});