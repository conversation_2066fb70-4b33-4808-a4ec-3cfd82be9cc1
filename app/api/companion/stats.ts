import axios from 'axios';
import * as SecureStore from 'expo-secure-store';
import { useQuery } from '@tanstack/react-query';
import { getAuthToken } from '@/app/api/companion/companion';

const BASE_URL = process.env.EXPO_PUBLIC_API_URL;

export interface SupplierStatsResponse {
  success: boolean;
  data: {
    user: {
      name: string;
      status: string;
      totalRatings: number;
      totalReviews: number;
    };
    data: {
      totalBookings: number;
      completedBookings: number;
      cancelledBookings: number;
      totalEarnings: number;
      thisMonthEarnings: number;
      lastMonthEarnings: number;
      profileViews: number;
      responseRate: number;
      responseTime: number;
      averageRating: number;
      totalReviews: number;
      profileCompletion: number;
      monthlyStats: {
        month: string;
        bookings: number;
        earnings: number;
        rating: number;
      }[];
      weeklyStats: {
        week: string;
        bookings: number;
        earnings: number;
        rating: number;
      }[];
      quarterStats: {
        quarter: string;
        bookings: number;
        earnings: number;
        rating: number;
      }[];
      servicePerformance: {
        name: string;
        bookings: number;
        rating: number;
        earnings: number;
      }[];
    };
  };
  message: string;
}

export const fetchSupplierStats = async (): Promise<SupplierStatsResponse> => {
  try {
    const token = await getAuthToken();
  const response = await axios.get(`${BASE_URL}/suppliers/stats`, {
    headers: {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
      ...(token && { 'Authorization': `Bearer ${token}` }),
    },
  });
  return response.data;
  } catch (error) {
    console.error('Error fetching supplier stats:', error);
    throw error;
  }
};

export const useSupplierStats = () => {
  return useQuery({
    queryKey: ['supplierStats'],
    queryFn: fetchSupplierStats,
  });
};
