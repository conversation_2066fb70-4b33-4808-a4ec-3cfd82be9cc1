import axios from 'axios';
import { useQuery, useMutation } from '@tanstack/react-query';
import { getAuthToken } from './companion';

const BASE_URL = process.env.EXPO_PUBLIC_API_URL;

// Types
export interface ExperienceStats {
  yearsOfExperience: number;
  totalGuests: number;
  averageRating: number;
  responseTime: string;
}

export interface SocialLinks {
  instagram?: string;
  facebook?: string;
  twitter?: string;
  tiktok?: string;
  website?: string;
  other?: { name: string; url: string }[];
  [key: string]: any;
}

export interface CompanionProfile {
  id: string;
  userId: string;
  firstName: string;
  lastName: string;
  displayName: string;
  coverPhoto?: string;
  profilePhoto?: string;
  bio?: string;
  socialLinks: SocialLinks;
  dateOfBirth?: string;
  gender?: string;
  location: string;
  languages: string[];
  specialization: string[];
  certifications: string[];
  experienceStats: ExperienceStats;
  createdAt: string;
  updatedAt: string;
}

export interface CompanionProfileResponse {
  success: boolean;
  data: CompanionProfile;
  message: string;
}

export interface CompanionProfileRequest {
  first_name: string;
  last_name: string;
  display_name: string;
  bio?: string;
  social_links?: SocialLinks;
  dateOfBirth?: string;
  gender?: 'male' | 'female' | 'other';
  cover_photo?: string;
  profile_photo?: string;
  location: string;
  languages: string[];
  specialization: string[];
  certifications: string[];
}

// GET /companion/profile
export const fetchCompanionProfile = async (): Promise<CompanionProfileResponse> => {
  const token = await getAuthToken();
  // console.log('BASE_URL:', BASE_URL);
  // console.log('Auth token:', token);
  try {
    const response = await axios.get(`${BASE_URL}/users/companion/profile`, {
      headers: {
        'Content-Type': 'application/json',
        ...(token && { 'Authorization': `Bearer ${token}` }),
      },
    });
    // console.log('Companion profile API response:', response.data);
    return response.data;
  } catch (error) {
    console.error('Error fetching companion profile:', error);
    if (axios.isAxiosError(error)) {
      console.error('Axios error response:', error.response?.data);
    }
    throw error;
  }
};

export const useCompanionProfile = () => {
  return useQuery({
    queryKey: ['companionProfile'],
    queryFn: fetchCompanionProfile,
  });
};

// POST /companion/profile
export const createOrUpdateCompanionProfile = async (payload: any): Promise<CompanionProfileResponse> => {
  const token = await getAuthToken();
  let headers: any = {
    ...(token && { 'Authorization': `Bearer ${token}` }),
  };
  let dataToSend = payload;
  let url = `${BASE_URL}/users/companion/profile`;
  let method = 'PUT';

  if (!(payload instanceof FormData)) {
    headers['Content-Type'] = 'application/json';
    dataToSend = JSON.stringify(payload);
  }
  const response = await axios({
    url,
    method,
    headers,
    data: dataToSend,
  });
  return response.data;
};

export const createCompanionProfile = async (payload: CompanionProfileRequest): Promise<CompanionProfileResponse> => {
  try {
    const token = await getAuthToken();
    const response = await axios.post(`${BASE_URL}/users/companion/profile`, payload, {
      headers: {
        'Content-Type': 'application/json',
        ...(token && { 'Authorization': `Bearer ${token}` }),
      },
    });
    return response.data;
  } catch (error) {
    console.error('Error creating or updating companion profile:', error);
    if (axios.isAxiosError(error)) {
      console.error('Axios error response:', error.response?.data);
    }
    throw error;
  }
  };

export const useCreateOrUpdateCompanionProfile = () => {
  return useMutation({
    mutationFn: createOrUpdateCompanionProfile,
  });
};
