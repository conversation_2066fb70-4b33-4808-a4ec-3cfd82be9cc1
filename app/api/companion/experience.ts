import axios from 'axios';
import { useMutation, useQuery } from '@tanstack/react-query';
import { getAuthToken } from './companion';

const BASE_URL = process.env.EXPO_PUBLIC_API_URL;

// Types
export interface Experience {
  id: string;
  title: string;
  description?: string;
  durationMinutes: number;
  keywords: string[];
  price: number;
  currency: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface ExperienceCreateRequest {
  title: string;
  description?: string;
  durationMinutes: number;
  keywords: string[];
  price: number;
  currency: string;
  is_active: boolean;
}

export interface ExperienceCreateResponse {
  success: boolean;
  data: {
    experienceId: string;
    created: boolean;
  };
  message: string;
}

export interface ExperienceListResponse {
  success: boolean;
  data: {
    items: Experience[];
    pagination: {
      page: number;
      limit: number;
      total: number;
      totalPages: number;
    };
  };
  message: string;
}



// POST /companions/:id/experiences
export const createExperience = async (companionId: string, payload: ExperienceCreateRequest): Promise<ExperienceCreateResponse> => {
 try {
  const token = await getAuthToken();
  // console.log("payload", payload);
  const response = await axios.post(`${BASE_URL}/companions/${companionId}/experiences`, payload, {
    headers: {
      'Content-Type': 'application/json',
      ...(token && { 'Authorization': `Bearer ${token}` }),
    },
  });

  // console.log("response", response.data);
  return response.data;
 } catch (error) {
  console.error('Error creating experience:', error);
  throw error;
 }
};

export const useCreateExperience = (companionId: string) => {
  return useMutation({
    mutationFn: (payload: ExperienceCreateRequest) => createExperience(companionId, payload),
  });
};

// GET /companions/:id/experiences
export const fetchExperiences = async (companionId: string): Promise<ExperienceListResponse> => {
 try {
  if (!companionId) {
    throw new Error('Companion ID is required');
  }

  // Log the request details
    // console.log('Fetching experiences for companion:', {
    //   companionId,
    //   url: `${BASE_URL}/companions/${companionId}/experiences`
    // });

  const token = await getAuthToken();
  const response = await axios.get(`${BASE_URL}/companions/${companionId}/experiences`, {
    headers: {
      'Content-Type': 'application/json',
      ...(token && { 'Authorization': `Bearer ${token}` }),
    },
  });

  // Log the response
  // console.log('Experiences API response:', response.data);

  return response.data;
 } catch (error) {
  const err = error as any;
  throw error;
 }
};

export const useExperiences = (companionId: string) => {
  return useQuery({
    queryKey: ['experiences', companionId],
    queryFn: () => fetchExperiences(companionId),
    enabled: !!companionId && companionId !== '',
    retry: false, // Don't retry on validation errors
  });
};
