import axios from "axios";
import * as SecureStore from "expo-secure-store";
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';

const BASE_URL = process.env.EXPO_PUBLIC_API_URL || 'https://tirak-backend.tirak-court.workers.dev';

// TypeScript interfaces for booking API
export interface CreateBookingRequest {
  companionId: string; // Must be UUID format
  serviceId?: string; // Must be UUID format
  date: string;
  startTime: string;
  endTime?: string;
  duration: number; // Duration in minutes (minimum 30)
  location?: string;
  specialRequests?: string;
  meetingPoint?: string;
  template?: string;
  preferredLanguages?: string[];
  dietaryRestrictions?: string[];
  accessibilityNeeds?: string[];
}

export interface BookingCompanion {
  id: string;
  name: string;
  profileImage: string;
  phone?: string;
  rating?: number;
}

export interface BookingCustomer {
  id: string;
  name: string;
  profileImage: string;
  phone: string;
  rating: number;
}

export interface BookingService {
  id: string;
  name: string;
  description?: string;
  price: number;
}

export interface PaymentMethod {
  id: string;
  type: string;
  last4?: string;
}

export interface BookingTimelineItem {
  status: string;
  timestamp: string;
  note?: string;
}

export type BookingStatus = "pending" | "confirmed" | "in_progress" | "completed" | "cancelled";
export type PaymentStatus = "pending" | "paid" | "refunded";

export interface Booking {
  meetingPoint: string;
  id: string;
  companionId: string;
  companion: BookingCompanion;
  customerId: string;
  customer?: BookingCustomer;
  serviceId?: string;
  service?: BookingService;
  date: string;
  startTime: string;
  endTime: string;
  duration: number;
  location?: string;
  specialRequests?: string;
  status: BookingStatus;
  totalAmount: number;
  serviceFee: number;
  paymentStatus: PaymentStatus;
  paymentMethod?: PaymentMethod;
  timeline?: BookingTimelineItem[];
  createdAt: string;
  updatedAt: string;
}

export interface BookingListItem {
  id: string;
  companion: BookingCompanion;
  customer: BookingCustomer;
  service?: BookingService;
  date: string;
  startTime: string;
  endTime: string;
  duration: number;
  location?: string;
  status: BookingStatus;
  totalAmount: number;
  paymentStatus: PaymentStatus;
  createdAt: string;
}

export interface Pagination {
  page: number;
  limit: number;
  total: number;
  totalPages: number;
}

export interface CreateBookingResponse {
  success: boolean;
  data: {
    booking: Booking;
  };
  message: string;
}

export interface BookingsListResponse {
  success: boolean;
  data: {
    // Some backends return 'bookings', some return 'items'
    bookings?: BookingListItem[];
    items?: BookingListItem[];
    pagination: Pagination;
  };
}

export interface BookingDetailsResponse {
  success: boolean;
  data: {
    booking: Booking;
  };
}

export interface UpdateBookingStatusRequest {
  status: "confirmed" | "cancelled" | "completed";
  reason?: string;
}

export interface UpdateBookingStatusResponse {
  success: boolean;
  data: {
    booking: Booking;
  };
  message: string;
}

// Query parameters for fetching bookings
export interface BookingsQueryParams {
  status?: BookingStatus;
  page?: number;
  limit?: number;
}

// Helper function to get auth token
const getAuthToken = async (): Promise<string | null> => {
  try {
    return await SecureStore.getItemAsync("authToken");
  } catch (error) {
    console.error("Error getting auth token:", error);
    return null;
  }
};

// API Functions

// Create a new booking
export const createBooking = async (bookingData: CreateBookingRequest): Promise<CreateBookingResponse> => {
  try {
    const token = await getAuthToken();
    
    const url = `${BASE_URL}/bookings`;
    
    // Validate required fields
    if (!bookingData.companionId || !bookingData.date || !bookingData.startTime || !bookingData.duration) {
      throw new Error('Missing required booking fields');
    }
    
    // Clean up optional arrays to prevent sending empty arrays
    const cleanedData = {
      ...bookingData,
      preferredLanguages: bookingData.preferredLanguages?.length ? bookingData.preferredLanguages : undefined,
      dietaryRestrictions: bookingData.dietaryRestrictions?.length ? bookingData.dietaryRestrictions : undefined,
      accessibilityNeeds: bookingData.accessibilityNeeds?.length ? bookingData.accessibilityNeeds : undefined
    };
    
    // console.log("Creating booking:", url, cleanedData); 
    // console.log("created booking"); 
    
    const response = await axios.post(url, cleanedData, {
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        ...(token && { 'Authorization': `Bearer ${token}` }),
      },
    });

    // console.log("Create booking response:", response.data);
    
    return response.data;
  } catch (error) {
    console.error("Error creating booking:", error);
    
    if (axios.isAxiosError(error)) {
      const errorMessage = error.response?.data?.message || 
                          error.response?.data?.error || 
                          error.message || 
                          "Failed to create booking";
      console.error("API Error Details:", {
        status: error.response?.status,
        data: error.response?.data,
        message: errorMessage
      });
      throw new Error(errorMessage);
    }
    
    throw error instanceof Error ? error : new Error("Network error occurred while creating booking");
  }
};

// Fetch bookings list
export const fetchBookings = async (params: BookingsQueryParams = {}): Promise<BookingsListResponse> => {
  try {
    const token = await getAuthToken();
    
    // Build query string
    const queryParams = new URLSearchParams();
    
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        queryParams.append(key, String(value));
      }
    });
    
    const queryString = queryParams.toString();
    const url = `${BASE_URL}/bookings${queryString ? `?${queryString}` : ''}`;
    
    // console.log("Fetching bookings:", url);
    
    const response = await axios.get(url, {
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        ...(token && { 'Authorization': `Bearer ${token}` }),
      },
    });

    // console.log("Bookings list response:", response.data);
    
    return response.data;
  } catch (error) {
    console.error("Error fetching bookings:", error);
    
    if (axios.isAxiosError(error)) {
      const errorMessage = error.response?.data?.message || 
                          error.response?.data?.error || 
                          error.message || 
                          "Failed to fetch bookings";
      console.error("API Error Details:", {
        status: error.response?.status,
        data: error.response?.data,
        message: errorMessage
      });
      throw new Error(errorMessage);
    }
    
    throw new Error("Network error occurred while fetching bookings");
  }
};

// Fetch booking details by ID
export const fetchBookingById = async (id: string): Promise<BookingDetailsResponse> => {
  try {
    const token = await getAuthToken();
    
    const url = `${BASE_URL}/bookings/${id}`;
    
    // console.log("Fetching booking details:", url);
    
    const response = await axios.get(url, {
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        ...(token && { 'Authorization': `Bearer ${token}` }),
      },
    });

    // console.log("Booking details response:", response.data);
    
    return response.data;
  } catch (error) {
    console.error("Error fetching booking details:", error);
    
    if (axios.isAxiosError(error)) {
      const errorMessage = error.response?.data?.message || 
                          error.response?.data?.error || 
                          error.message || 
                          "Failed to fetch booking details";
      console.error("API Error Details:", {
        status: error.response?.status,
        data: error.response?.data,
        message: errorMessage
      });
      throw new Error(errorMessage);
    }
    
    throw new Error("Network error occurred while fetching booking details");
  }
};

// Update booking status
export const updateBookingStatus = async (
  id: string, 
  statusData: UpdateBookingStatusRequest
): Promise<UpdateBookingStatusResponse> => {
  try {
    const token = await getAuthToken();
    
    const url = `${BASE_URL}/bookings/${id}/status`;
    
    // console.log("Updating booking status:", url, statusData);
    
    const response = await axios.put(url, statusData, {
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        ...(token && { 'Authorization': `Bearer ${token}` }),
      },
    });

    // console.log("Update booking status response:", response.data);
    
    return response.data;
  } catch (error) {
    console.error("Error updating booking status:", error);
    
    if (axios.isAxiosError(error)) {
      const errorMessage = error.response?.data?.message || 
                          error.response?.data?.error || 
                          error.message || 
                          "Failed to update booking status";
      console.error("API Error Details:", {
        status: error.response?.status,
        data: error.response?.data,
        message: errorMessage
      });
      throw new Error(errorMessage);
    }
    
    throw new Error("Network error occurred while updating booking status");
  }
};

// React Query Hooks

// Hook for creating a booking
export const useCreateBooking = () => {
  const queryClient = useQueryClient();
  
  // console.log('=== useCreateBooking hook initialized ===');
  
  return useMutation({
    mutationKey: ['createBooking'],
    mutationFn: async (bookingData: CreateBookingRequest) => {
      console.log('🚀 [API Call Start] createBooking mutation triggered'  , {
        timestamp: new Date().toISOString(),
        bookingData: {
          companionId: bookingData.companionId,
          serviceId: bookingData.serviceId,
          date: bookingData.date,
          startTime: bookingData.startTime,
          duration: bookingData.duration
        }
      });
      
      const token = await getAuthToken();
      
      const url = `${BASE_URL}/bookings`;
      
      // Validate required fields
      if (!bookingData.companionId || !bookingData.date || !bookingData.startTime || !bookingData.duration) {
        // console.error('❌ Validation failed: Missing required booking fields'); 
        throw new Error('Missing required booking fields');
      }
      
      // Clean up optional arrays to prevent sending empty arrays
      const cleanedData = {
        ...bookingData,
        preferredLanguages: bookingData.preferredLanguages?.length ? bookingData.preferredLanguages : undefined,
        dietaryRestrictions: bookingData.dietaryRestrictions?.length ? bookingData.dietaryRestrictions : undefined,
        accessibilityNeeds: bookingData.accessibilityNeeds?.length ? bookingData.accessibilityNeeds : undefined
      };
      
      console.log("📤 Sending API request:", {
        url,
        method: 'POST',
        timestamp: new Date().toISOString(),
        data: cleanedData
      });
      
      const response = await axios.post(url, cleanedData, {
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
          ...(token && { 'Authorization': `Bearer ${token}` }),
        },
      });

      console.log("📥 API Response received:", {
        timestamp: new Date().toISOString(),
        status: response.status,
        data: response.data,
        success: response.data?.success,
        bookingId: response.data?.data?.booking?.id
      });

      // Validate response format
      if (!response.data?.success || !response.data?.data?.booking?.id) {
        console.error("❌ Invalid API response format:", {
          timestamp: new Date().toISOString(),
          response: response.data
        });
        throw new Error('Invalid API response format');
      }
      
      return response.data;
    },
    onSuccess: (data) => {
      console.log("✅ Mutation succeeded:", {
        timestamp: new Date().toISOString(),
        success: data.success,
        bookingId: data.data?.booking?.id
      });
      // Invalidate and refetch bookings list
      queryClient.invalidateQueries({ queryKey: ['bookings'] });
    },
    onError: (error: Error) => {
      console.error("❌ Mutation failed:", {
        timestamp: new Date().toISOString(),
        error: error.message
      });
      throw error; // Re-throw to be caught by the component
    },
  });
};

// Hook for fetching bookings list
export const useBookingsQuery = (params: BookingsQueryParams = {}, options?: { enabled?: boolean }) => {
  return useQuery({
    queryKey: ['bookings', params],
    queryFn: () => fetchBookings(params),
    staleTime: 2 * 60 * 1000, // 2 minutes
    gcTime: 5 * 60 * 1000, // 5 minutes
    refetchOnWindowFocus: true, // Refetch when user returns to app
    enabled: options?.enabled !== false, // Default to true, allow override
    retry: (failureCount: number, error: Error) => {
      // Don't retry on authentication errors
      if (error.message.includes('401') || error.message.includes('Unauthorized')) {
        return false;
      }
      // Retry up to 3 times for other errors
      return failureCount < 3;
    },
  });
};

// Hook for fetching booking details
export const useBookingQuery = (id: string) => {
  return useQuery({
    queryKey: ['booking', id],
    queryFn: () => fetchBookingById(id),
    staleTime: 1 * 60 * 1000, // 1 minute
    gcTime: 3 * 60 * 1000, // 3 minutes
    refetchOnWindowFocus: true,
    enabled: !!id, // Only run query if ID is provided
    retry: (failureCount: number, error: Error) => {
      // Don't retry on authentication errors or 404s
      if (error.message.includes('401') || error.message.includes('404') || error.message.includes('Unauthorized')) {
        return false;
      }
      // Retry up to 2 times for other errors
      return failureCount < 2;
    },
  });
};

// Hook for updating booking status
export const useUpdateBookingStatus = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: ({ id, statusData }: { id: string; statusData: UpdateBookingStatusRequest }) =>
      updateBookingStatus(id, statusData),
    onSuccess: (data, variables) => {
      // Invalidate and refetch bookings list
      queryClient.invalidateQueries({ queryKey: ['bookings'] });
      // Invalidate and refetch specific booking details
      queryClient.invalidateQueries({ queryKey: ['booking', variables.id] });
      console.log("Booking status updated successfully:", data.data.booking.status);
    },
    onError: (error: Error) => {
      console.error("Failed to update booking status:", error.message);
    },
  });
};

// Convenience hooks for specific booking statuses
export const usePendingBookings = () => {
  return useBookingsQuery({ status: 'pending' });
};

export const useConfirmedBookings = () => {
  return useBookingsQuery({ status: 'confirmed' });
};

export const useCompletedBookings = () => {
  return useBookingsQuery({ status: 'completed' });
};

export const useCancelledBookings = () => {
  return useBookingsQuery({ status: 'cancelled' });
};

// Hook for bookings with pagination
export const useBookingsWithPagination = (page: number = 1, limit: number = 10, status?: BookingStatus) => {
  return useBookingsQuery({ page, limit, status });
};

// Convenience functions for direct API calls (without React Query)
export const getBookingDetails = (id: string) => {
  return fetchBookingById(id);
};

export const getUserBookings = (params: BookingsQueryParams = {}) => {
  return fetchBookings(params);
};

export const createNewBooking = (bookingData: CreateBookingRequest) => {
  return createBooking(bookingData);
};

export const changeBookingStatus = (id: string, statusData: UpdateBookingStatusRequest) => {
  return updateBookingStatus(id, statusData);
};