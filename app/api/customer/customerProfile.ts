import axios from 'axios';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';

const BASE_URL = process.env.EXPO_PUBLIC_API_URL;

// Optionally import getAuthToken if you use auth
import { getAuthToken } from '../companion/companion';

// Types
export interface CustomerProfile {
  id: string;
  name: string;
  email: string;
  role: 'customer' | 'companion';
  verified: boolean;
  profileImage?: string;
  phone?: string;
  dateOfBirth?: string;
  gender?: string;
  preferences: {
    language: string;
    currency: string;
    notifications: {
      push: boolean;
      email: boolean;
      sms: boolean;
    };
  };
  createdAt: string;
  updatedAt: string;
}

export interface CustomerProfileResponse {
  success: boolean;
  data: CustomerProfile;
  message?: string;
}

// GET /users/profile
export const fetchCustomerProfile = async (): Promise<CustomerProfileResponse> => {
  const token = await getAuthToken?.();
  try {
    const response = await axios.get(`${BASE_URL}/users/profile`, {
      headers: {
        'Content-Type': 'application/json',
        ...(token && { 'Authorization': `Bearer ${token}` }),
      },
    });
    return response.data;
  } catch (error) {
    console.error('Error fetching customer profile:', error);
    if (axios.isAxiosError(error)) {
      console.error('Axios error response:', error.response?.data);
    }
    throw error;
  }
};

export const useCustomerProfile = (options?: any) => {
  return useQuery({
    queryKey: ['customerProfile'],
    queryFn: fetchCustomerProfile,
    ...options,
  });
};

// PUT /users/profile
export const updateCustomerProfile = async (payload: any): Promise<CustomerProfileResponse> => {
  const token = await getAuthToken?.();
  let headers: any = {
    ...(token && { 'Authorization': `Bearer ${token}` }),
  };
  let dataToSend = payload;
  let url = `${BASE_URL}/users/profile`;
  let method = 'PUT';

  if (!(payload instanceof FormData)) {
    headers['Content-Type'] = 'application/json';
    dataToSend = JSON.stringify(payload);
  }
  try {
    const response = await axios({
      url,
      method,
      headers,
      data: dataToSend,
    });
    return response.data;
  } catch (error) {
    console.error('Error updating customer profile:', error);
    if (axios.isAxiosError(error)) {
      console.error('Axios error response:', error.response?.data);
    }
    throw error;
  }
};

export const useUpdateCustomerProfile = (options?: any) => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: updateCustomerProfile,
    ...options,
    onSuccess: (data: any, variables: any, context: any) => {
      queryClient.invalidateQueries({ queryKey: ['customerProfile'] });
      if (options && options.onSuccess) options.onSuccess(data, variables, context);
    },
  });
};
