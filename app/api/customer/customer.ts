import axios from 'axios';
import { useQuery } from '@tanstack/react-query';
import { getAuthToken } from '../companion/companion';

const BASE_URL = process.env.EXPO_PUBLIC_API_URL;

// Types
export interface Customer {
  id: string;
  email: string;
  phone: string;
  displayName: string;
  profileImage: string;
  status: string;
  loyaltyPoints: number;
  emailVerified: boolean;
  phoneVerified: boolean;
  preferredLanguage: string;
  createdAt: string;
  lastLoginAt: string;
  preferences: Record<string, any>;
}

export interface CustomerListResponse {
  success: boolean;
  data: Customer[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
  message: string;
}

export interface CustomerQueryParams {
  search?: string;
  status?: string;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
  page?: number;
  limit?: number;
}

export interface CustomerProfile {
  id: string;
  displayName: string;
  profileImage: string;
  loyaltyPoints: number;
  preferences: Record<string, any>;
  memberSince: string;
  statistics: {
    totalBookings: number;
    completedBookings: number;
    pendingBookings: number;
    cancelledBookings: number;
    favoriteSuppliers: number;
  };
  language: string;
  emailVerified: boolean;
  phoneVerified: boolean;
}

export interface CustomerProfileResponse {
  success: boolean;
  data: CustomerProfile;
  message: string;
}

// GET /customers/all
export const fetchCustomers = async (params: CustomerQueryParams = {}): Promise<CustomerListResponse> => {
 try {
    const token = await getAuthToken();
    const queryParams = new URLSearchParams();
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        queryParams.append(key, String(value));
      }
    });
    const url = `${BASE_URL}/customers/all${queryParams.toString() ? `?${queryParams}` : ''}`;
    // console.log('url:', url); 
    const response = await axios.get(url, {
      headers: {
        'Content-Type': 'application/json',
        ...(token && { 'Authorization': `Bearer ${token}` }),
      },
    });
    return response.data;
 } catch (error) {
  console.error('Error fetching customers:', error);
  throw error;
 }  
};

export const useCustomers = (params: CustomerQueryParams = {}) => {
  return useQuery({
    queryKey: ['customers', params],
    queryFn: () => fetchCustomers(params),
  });
};

// GET /customers/:id
export const fetchCustomerProfile = async (id: string): Promise<CustomerProfileResponse> => {
try {
    const token = await getAuthToken();
  const url = `${BASE_URL}/customers/${id}`;
  const response = await axios.get(url, {
    headers: {
      'Content-Type': 'application/json',
      ...(token && { 'Authorization': `Bearer ${token}` }),
    },
  });
  return response.data;
} catch (error) {
  console.error('Error fetching customer profile:', error);
  throw error;
}
};

export const useCustomerProfile = (id: string) => {
  return useQuery({
    queryKey: ['customer', id],
    queryFn: () => fetchCustomerProfile(id),
    enabled: !!id,
  });
};
