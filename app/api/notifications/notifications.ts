import axios from 'axios';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { getAuthToken } from '../companion/companion';

const BASE_URL = process.env.EXPO_PUBLIC_API_URL;

// Types
export type NotificationType =
    | 'booking_confirmed'
    | 'booking_cancelled'
    | 'new_message'
    | 'review_received'
    | 'payment_completed';

export interface NotificationData {
    bookingId?: string;
    conversationId?: string;
    reviewId?: string;
    paymentId?: string;
}

export interface Notification {
    id: string;
    type: NotificationType;
    title: string;
    message: string;
    data: NotificationData;
    read: boolean;
    createdAt: string;
}

export interface NotificationsResponse {
    success: boolean;
    data: {
        notifications: Notification[];
        pagination: {
            page: number;
            limit: number;
            total: number;
            totalPages: number;
        };
        unreadCount: number;
    };
}

// API functions
export const getNotifications = async (params: { page?: number; limit?: number; read?: boolean } = {}): Promise<NotificationsResponse> => {
    try {
        const token = await getAuthToken();
        const response = await axios.get(`${BASE_URL}/notifications`, { params, headers: { Authorization: `Bearer ${token}`, 'Content-Type': 'application/json' } });
        // console.log("response", response.data); 
        return response.data;
    } catch (error) {
        console.error('Error fetching notifications:', error);
        throw error;
    }
};

export const markNotificationRead = async (id: string): Promise<{ success: boolean; message: string }> => {
    try {
        const token = await getAuthToken();
        const response = await axios.put(`${BASE_URL}/notifications/${id}/read`, { headers: { Authorization: `Bearer ${token}`, 'Content-Type': 'application/json' } });
        // console.log("response", response.data); 
        return response.data;
    } catch (error) {
        console.error('Error marking notification as read:', error);
        throw error;
    }
};

export const markAllNotificationsRead = async (): Promise<{ success: boolean; message: string }> => {
    try {
        const token = await getAuthToken();
        const response = await axios.put(`${BASE_URL}/notifications/read-all`, { headers: { Authorization: `Bearer ${token}`, 'Content-Type': 'application/json' } });
        // console.log("response", response.data);     
        return response.data;
    } catch (error) {
        console.error('Error marking all notifications as read:', error);
        throw error;
    }
};

// React Query hooks
export const useNotifications = (params: { page?: number; limit?: number; read?: boolean } = {}) => {
    return useQuery({
        queryKey: ['notifications', params],
        queryFn: () => getNotifications(params),
    });
};

export const useMarkNotificationRead = () => {
    const queryClient = useQueryClient();
    return useMutation({
        mutationFn: (id: string) => markNotificationRead(id),
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['notifications'] });
        },
    });
};

export const useMarkAllNotificationsRead = () => {
    const queryClient = useQueryClient();
    return useMutation({
        mutationFn: () => markAllNotificationsRead(),
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['notifications'] });
        },
    });
};
