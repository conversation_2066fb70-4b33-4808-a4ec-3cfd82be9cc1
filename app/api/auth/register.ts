import axios from "axios";

const BASE_URL = process.env.EXPO_PUBLIC_API_URL;

// TypeScript interface for registration data
interface RegisterData {
  display_name: string;
  email: string;
  password: string;
  userType: "customer" | "companion" | "supplier";
  phone?: string;
  dateOfBirth?: string;
  gender?: "male" | "female" | "other" | "prefer_not_to_say";
}

// Response interface
interface RegisterResponse {
  success: boolean;
  message: string;
  user?: {
    id: string;
    name: string;
    email: string;
    userType: string;
    verified?: boolean;
    createdAt?: string;
  };
  token?: string;
  refreshToken?: string;
}

export const register = async (userData: RegisterData): Promise<RegisterResponse> => {
  try {
    // console.log("userData", userData);
    const response = await axios.post(`${BASE_URL}/auth/register`, userData);

    // console.log("response", response.data);
    
    return response.data;
  } catch (error) {
    console.error("Error registering user:", error);
    
    // Handle different error types
    if (axios.isAxiosError(error)) {
      throw new Error(error.response?.data?.message || "Registration failed");
    }
    
    throw new Error("Network error occurred");
  }
};