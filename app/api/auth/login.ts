import axios from "axios";

const BASE_URL = process.env.EXPO_PUBLIC_API_URL;

// TypeScript interface for login data
interface LoginData {
  identifier: string;
  password: string;
}

// Response interface matching actual backend response
interface LoginResponse {
  success: boolean;
  message: string;
  data?: {
    accessToken: string;
    refreshToken: string;
    expiresIn: number;
    user: {
    id: string;
    name: string;
    email: string;
      userType: string;
      phone?: string;
      emailVerified?: boolean;
      phoneVerified?: boolean;
      status?: string;
    };
  };
}

export const login = async (userData: LoginData): Promise<LoginResponse> => {
  try {
    // console.log("userData", userData);
    const response = await axios.post(`${BASE_URL}/auth/login`, userData);

    // console.log("response from login", response.data);
    
    return response.data;
  } catch (error) {
    console.error("Error logging in user:", error);
    
    // Handle different error types
    if (axios.isAxiosError(error)) {
      throw new Error(error.response?.data?.message || "Login failed");
    }
    
    throw new Error("Network error occurred");
  }
};