import React, { useEffect } from 'react';
import { Platform } from 'react-native';
import { Stack, router } from 'expo-router';
import { useFonts } from 'expo-font';
import * as SplashScreen from 'expo-splash-screen';
import { StatusBar } from 'expo-status-bar';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import * as Sentry from '@sentry/react-native';

Sentry.init({
  dsn: 'https://<EMAIL>/4509643525783552',

  // Adds more context data to events (IP address, cookies, user, etc.)
  // For more information, visit: https://docs.sentry.io/platforms/react-native/data-management/data-collected/
  sendDefaultPii: true,

  // Configure Session Replay
  replaysSessionSampleRate: 0.1,
  replaysOnErrorSampleRate: 1,
  integrations: [Sentry.mobileReplayIntegration(), Sentry.feedbackIntegration()],

  // uncomment the line below to enable Spotlight (https://spotlightjs.com)
  // spotlight: __DEV__,
});

// Create a client
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 5 * 60 * 1000, // 5 minutes
      retry: (failureCount, error) => {
        // Don't retry on authentication errors
        if (error instanceof Error && (error.message.includes('401') || error.message.includes('Unauthorized'))) {
          return false;
        }
        // Don't retry on network errors
        if (error instanceof Error && error.message.includes('Network Error')) {
          return false;
        }

        // Retry up to 3 times
        return failureCount < 2;
      },
    },
  },
});

// Add a splash screen route
export { default as SplashScreen } from './splash';

// Prevent the splash screen from auto-hiding
SplashScreen.preventAutoHideAsync();

export default Sentry.wrap(function RootLayout() {
  const [fontsLoaded] = useFonts({
    // Custom fonts for headings and subheadings only (visual impact)
    'ProximaNova-Regular': require('../assets/images/fonts/ProximaNova-Regular.otf'),
    'ProximaNova-Semibold': require('../assets/images/fonts/Proxima Nova Semibold.otf'),
    'ProximaNova-Thin': require('../assets/images/fonts/Proxima Nova Thin.otf'),
    'Garet-Heavy': require('../assets/images/fonts/garet.heavy.ttf'),
  });

  // First effect just handles the splash screen
  useEffect(() => {
    if (fontsLoaded) {
      // Hide the expo splash screen once fonts are loaded
      SplashScreen.hideAsync();
    }
  }, [fontsLoaded]);

  // Second effect handles navigation after component is mounted
  useEffect(() => {
    // Use a timeout to ensure navigation happens after mounting
    const timer = setTimeout(() => {
      if (fontsLoaded) {
        router.replace('/splash');
      }
    }, 100);
    
    return () => clearTimeout(timer);
  }, [fontsLoaded]);

  if (!fontsLoaded) {
    return null;
  }

  return (
    <QueryClientProvider client={queryClient}>
      <StatusBar style={Platform.OS === 'ios' ? 'dark' : 'auto'} />
      <RootLayoutNav />
    </QueryClientProvider>
  );
});

function RootLayoutNav() {
  return (
    <Stack screenOptions={{ headerShown: false }}>
      <Stack.Screen name="splash" />
      <Stack.Screen name="onboarding" />
      <Stack.Screen name="auth" />
      <Stack.Screen name="(app)" />
      <Stack.Screen name="(supplier)" />
      <Stack.Screen name="supplier" />
    </Stack>
  );
}