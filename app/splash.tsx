import React, { useEffect } from 'react';
import { View, Text, StyleSheet, Animated, Image } from 'react-native';
import { router } from 'expo-router'; 
import { Logo } from '@/components/ui/Logo';
import { RadialGradient } from '@/components/ui/RadialGradient';
import { designTokens } from '@/constants/design-tokens';
import { useAuthStoreHydrated } from '@/hooks/useAuthStoreHydrated';

export default function SplashScreen() {
  const fadeAnim = new Animated.Value(0);
  const scaleAnim = new Animated.Value(0.8);
  const { isHydrated, isAuthenticated, onboarded } = useAuthStoreHydrated();

  useEffect(() => {
    // Logo animation
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 1000,
        useNativeDriver: true,
      }),
      Animated.spring(scaleAnim, {
        toValue: 1,
        tension: 50,
        friction: 7,
        useNativeDriver: true,
      }),
    ]).start();
  }, []);

  useEffect(() => {
    // Wait for auth store to hydrate before making navigation decisions
    if (!isHydrated) return;

    // Navigation timer - check auth state and navigate accordingly
    const timer = setTimeout(() => {
      if (isAuthenticated) {
        // User is logged in, go directly to the app
        router.replace('/(app)');
      } else if (onboarded) {
        // User has seen onboarding but not logged in, go to auth
        router.replace('/auth');
      } else {
        // New user, show onboarding
      router.replace('/onboarding');
      }
    }, 2500);

    return () => clearTimeout(timer);
  }, [isHydrated, isAuthenticated, onboarded]);

  return (
    <RadialGradient variant="primary" style={styles.container}>
      <Animated.View
        style={[
          styles.content,
          {
            opacity: fadeAnim,
            transform: [{ scale: scaleAnim }],
          },
        ]}
      >
       <Image source={require('@/assets/images/tirak-logo.png')} style={styles.logo} />
        <Text style={styles.tagline}>Private connections. Trusted moments.</Text>
      </Animated.View>
    </RadialGradient>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  content: {
    alignItems: 'center',
  },
  tagline: {
    fontSize: 16,
    color: designTokens.colors.semantic.text,
    textAlign: 'center',
    fontWeight: '500',
    marginTop: 20,
    opacity: 0.9,
  },
  logo: {
    width: 100,
    height: 100,
  },
});
