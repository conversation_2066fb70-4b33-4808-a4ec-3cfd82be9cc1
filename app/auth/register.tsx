import React, { useState, useEffect, useRef } from "react";
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  KeyboardAvoidingView,
  Platform,
  ColorValue,
  Image,
  Animated,
  Easing,
  TextInput,
} from "react-native";
import { LinearGradient } from "expo-linear-gradient";
import { router, useLocalSearchParams } from "expo-router";
import { z } from "zod";
import { Logo } from "@/components/ui/Logo";
import { Input } from "@/components/ui/Input";
import { SimpleInput } from "@/components/ui/SimpleInput";
import { Button } from "@/components/ui/Button";
import { designTokens } from "@/constants/design-tokens";
import { ArrowLeft, Calendar, ChevronDown } from "lucide-react-native";
import { UserRole } from "@/types/auth";
import DateTimePicker from "@react-native-community/datetimepicker";
import { useAuthStore } from "@/stores/auth-store";

const AnimatedLinearGradient = Animated.createAnimatedComponent(LinearGradient);

// Zod schema for registration form validation
const registerSchema = z
  .object({
  name: z
    .string()
      .min(1, "Name is required")
      .min(2, "Name must be at least 2 characters")
      .max(50, "Name must be less than 50 characters"),
  email: z
    .string()
      .min(1, "Email is required")
      .email("Please enter a valid email address"),
    contactNumber: z
      .string()
      .min(1, "Contact number is required")
      .min(10, "Contact number must be at least 10 digits")
      .regex(/^[0-9+\-\s()]+$/, "Please enter a valid contact number"),
    dateOfBirth: z.date().refine((date) => {
      const age = new Date().getFullYear() - date.getFullYear();
      return age >= 18;
    }, "You must be at least 18 years old"),
    gender: z.string().min(1, "Gender is required"),
    role: z.string().optional(),
  password: z
    .string()
      .min(1, "Password is required")
      .min(6, "Password must be at least 6 characters")
      .max(100, "Password must be less than 100 characters"),
    confirmPassword: z.string().min(1, "Please confirm your password"),
  })
  .refine((data) => data.password === data.confirmPassword, {
  message: "Passwords don't match",
  path: ["confirmPassword"],
});

type RegisterFormData = z.infer<typeof registerSchema>;

type FormErrors = {
  [K in keyof RegisterFormData]?: string;
};

export default function RegisterScreen() {
  const { role: roleParam } = useLocalSearchParams();
  const { register, isLoading, error: authError } = useAuthStore();

  
  const [formData, setFormData] = useState<RegisterFormData>({
    name: "",
    email: "",
    contactNumber: "",
    dateOfBirth: new Date(2000, 0, 1), // Default date
    gender: "",
    password: "",
    confirmPassword: "",
    role: roleParam === "companion" ? "" : undefined,
  });
  const [role, setRole] = useState<UserRole>("customer");
  const [errors, setErrors] = useState<FormErrors>({});
  const [showDatePicker, setShowDatePicker] = useState(false);
  const [showGenderPicker, setShowGenderPicker] = useState(false);
  const [showRolePicker, setShowRolePicker] = useState(false);

  const animatedValue = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    const animation = Animated.loop(
      Animated.sequence([
        Animated.timing(animatedValue, {
          toValue: 1,
          duration: 10000,
          easing: Easing.linear,
          useNativeDriver: false,
        }),
        Animated.timing(animatedValue, {
          toValue: 0,
          duration: 10000,
          easing: Easing.linear,
          useNativeDriver: false,
        }),
      ])
    );
    animation.start();
    
    // Cleanup animation on unmount
    return () => animation.stop();
  }, []);

  // Create a slow diagonal pan effect - memoized to prevent re-renders
  const gradientAnimation = React.useMemo(() => ({
    startX: animatedValue.interpolate({
    inputRange: [0, 1],
    outputRange: [0, -1],
    }),
    startY: animatedValue.interpolate({
    inputRange: [0, 1],
    outputRange: [0, -1],
    }),
    endX: animatedValue.interpolate({
    inputRange: [0, 1],
    outputRange: [1, 2],
    }),
    endY: animatedValue.interpolate({
    inputRange: [0, 1],
    outputRange: [1, 2],
    }),
  }), [animatedValue]);

  const validateForm = (): boolean => {
    try {
      registerSchema.parse(formData);
      
      // Additional validation for role when roleParam is companion
      if (roleParam === "companion" && !formData.role) {
        setErrors({ role: "Account type is required" });
        return false;
      }
      
      setErrors({});
      return true;
    } catch (error) {
      if (error instanceof z.ZodError) {
        const fieldErrors: FormErrors = {};
        error.errors.forEach((err) => {
          if (err.path[0]) {
            fieldErrors[err.path[0] as keyof RegisterFormData] = err.message;
          }
        });
        
        // Add custom role validation error if needed
        if (roleParam === "companion" && !formData.role) {
          fieldErrors.role = "Account type is required";
        }
        
        setErrors(fieldErrors);
      }
      return false;
    }
  };

  const handleInputChange = React.useCallback((
    field: keyof RegisterFormData,
    value: string | Date
  ) => {
    setFormData((prev) => ({ ...prev, [field]: value }));
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors((prev) => ({ ...prev, [field]: undefined }));
    }
  }, [errors]);

  const handleDateChange = (event: any, selectedDate?: Date) => {
    // For Android, close on any interaction (set or dismissed)
    // For iOS, we'll handle it differently with modal approach
    if (Platform.OS === "android") {
      setShowDatePicker(false);
      if (selectedDate && event.type === "set") {
        handleInputChange("dateOfBirth", selectedDate);
      }
    } else {
      // For iOS, update date immediately but don't close picker
      if (selectedDate) {
        handleInputChange("dateOfBirth", selectedDate);
      }
    }
  };

  const handleDatePickerDone = React.useCallback(() => {
    setShowDatePicker(false);
  }, []);

  // Memoized input handlers to prevent re-renders
  const handleNameChange = React.useCallback((text: string) => {
    handleInputChange("name", text);
  }, [handleInputChange]);

  const handleEmailChange = React.useCallback((text: string) => {
    handleInputChange("email", text);
  }, [handleInputChange]);

  const handleContactChange = React.useCallback((text: string) => {
    handleInputChange("contactNumber", text);
  }, [handleInputChange]);

  const handlePasswordChange = React.useCallback((text: string) => {
    handleInputChange("password", text);
  }, [handleInputChange]);

  const handleConfirmPasswordChange = React.useCallback((text: string) => {
    handleInputChange("confirmPassword", text);
  }, [handleInputChange]);

  const genderOptions = [
    { label: "Male", value: "male" },
    { label: "Female", value: "female" },
    { label: "Other", value: "other" },
  ];

  const roleOptions = [
    { label: "Supplier", value: "supplier" },
    { label: "Companion", value: "companion" },
  ];

  const handleRegister = async () => {
    if (validateForm()) {
      // Determine the actual role to register
      let actualUserType: "customer" | "companion" | "supplier";
      if (roleParam === "companion") {
        // If roleParam is companion, use the selected role from form (supplier or companion)
        actualUserType = (formData.role as string) === "supplier" ? "supplier" : "companion";
      } else {
        // If roleParam is not companion, always register as customer
        actualUserType = "customer";
      }
      
      try {
        await register(
          formData.name,
          formData.email,
          formData.password,
          actualUserType,
          formData.contactNumber,
          formData.dateOfBirth,
          formData.gender
        );
        // Only navigate if registration was successful (no exception thrown)
        router.replace('/(app)');
      } catch (err) {
        console.error('Registration error:', err);
        // Don't navigate on error - stay on registration screen to show error
      }
    }
  };

  const handleBack = () => {
    router.back();
  };

  return (
    <KeyboardAvoidingView
      style={{ flex: 1 }}
      behavior={Platform.OS === "ios" ? "padding" : undefined}
    >
      <LinearGradient
        colors={[
          `${designTokens.colors.semantic.primary}70`,
          `${designTokens.colors.reference.lightPink}60`,
          `${designTokens.colors.semantic.secondary}70`,
          `${designTokens.colors.reference.lightPink}60`,
          `${designTokens.colors.semantic.primary}70`,
        ]}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
        style={styles.container}
      >
        <ScrollView
          contentContainerStyle={styles.scrollContent}
          keyboardShouldPersistTaps="always"
          showsVerticalScrollIndicator={false}
        >
          <TouchableOpacity style={styles.backButton} onPress={handleBack}>
            <ArrowLeft size={24} color={designTokens.colors.semantic.text} />
          </TouchableOpacity>
          
          <View style={styles.header}>
            <Image
              source={require("@/assets/images/tirak.png")}
              style={styles.logo}
            />
            <Text style={styles.title}>Create Account</Text>
            <Text style={styles.subtitle}>Join Tirak today</Text>
          </View>
          
          <View style={styles.form}>
            {authError && (
              <View style={styles.errorContainer}>
                <Text style={styles.errorText}>{authError}</Text>
              </View>
            )}
            
            <SimpleInput
              label="Full Name"
              placeholder="Enter your full name"
              value={formData.name}
              onChangeText={handleNameChange}
              error={errors.name}
            />
            
            <SimpleInput
              label="Email"
              placeholder="Enter your email"
              value={formData.email}
              onChangeText={handleEmailChange}
              keyboardType="email-address"
              autoCapitalize="none"
              error={errors.email}
            />
            
            <SimpleInput
              label="Contact Number"
              placeholder="Enter your contact number"
              value={formData.contactNumber}
              onChangeText={handleContactChange}
              keyboardType="phone-pad"
              error={errors.contactNumber}
            />

            <Text style={styles.datePickerLabel}>Date of Birth</Text>

            <TouchableOpacity
              style={styles.datePickerButton}
              onPress={() => setShowDatePicker(true)}
            >
              <View style={styles.datePickerContent}>
                <View style={styles.datePickerValue}>
                  <Text style={styles.datePickerText}>
                    {formData.dateOfBirth.toLocaleDateString()}
                  </Text>
                  <Calendar
                    size={20}
                    color={designTokens.colors.semantic.primary}
                  />
                </View>
              </View>
              {errors.dateOfBirth && (
                <Text style={styles.fieldError}>{errors.dateOfBirth}</Text>
              )}
            </TouchableOpacity>

            {showDatePicker && (
              <>
                {Platform.OS === "ios" ? (
                  <View style={styles.datePickerModal}>
                    <View style={styles.datePickerHeader}>
                      <TouchableOpacity onPress={handleDatePickerDone}>
                        <Text style={styles.datePickerDoneButton}>Done</Text>
                      </TouchableOpacity>
                    </View>
                    <DateTimePicker
                      value={formData.dateOfBirth}
                      mode="date"
                      display="spinner"
                      onChange={handleDateChange}
                      maximumDate={new Date()}
                      style={styles.datePickerIOS}
                    />
                  </View>
                ) : (
                  <DateTimePicker
                    value={formData.dateOfBirth}
                    mode="date"
                    display="default"
                    onChange={handleDateChange}
                    maximumDate={new Date()}
                  />
                )}
              </>
            )}

            <Text style={styles.genderPickerLabel}>Gender</Text>
            <TouchableOpacity
              style={styles.genderPickerButton}
              onPress={() => setShowGenderPicker(!showGenderPicker)}
            >
              <View style={styles.genderPickerContent}>
                <View style={styles.genderPickerValue}>
                  <Text style={styles.genderPickerText}>
                    {formData.gender
                      ? genderOptions.find((g) => g.value === formData.gender)
                          ?.label
                      : "Select gender"}
                  </Text>
                  <ChevronDown
                    size={20}
                    color={designTokens.colors.semantic.primary}
                  />
                </View>
              </View>
              {errors.gender && (
                <Text style={styles.fieldError}>{errors.gender}</Text>
              )}
            </TouchableOpacity>

            {showGenderPicker && (
              <View style={styles.genderOptions}>
                {genderOptions.map((option) => (
                  <TouchableOpacity
                    key={option.value}
                    style={styles.genderOption}
                    onPress={() => {
                      handleInputChange("gender", option.value);
                      setShowGenderPicker(false);
                    }}
                  >
                    <Text style={styles.genderOptionText}>{option.label}</Text>
                  </TouchableOpacity>
                ))}
              </View>
            )}
            
            {roleParam === "companion" && (
              <>
            <Text style={styles.genderPickerLabel}>Type of Account</Text>
            <TouchableOpacity
              style={styles.genderPickerButton}
              onPress={() => setShowRolePicker(!showRolePicker)}
            >
              <View style={styles.genderPickerContent}>
                <View style={styles.genderPickerValue}>
                  <Text style={styles.genderPickerText}>
                    {formData.role
                      ? roleOptions.find((g) => g.value === formData.role)
                          ?.label
                      : "Select type of account"}
                  </Text>
                  <ChevronDown
                    size={20}
                    color={designTokens.colors.semantic.primary}
                  />
                </View>
              </View>
              {errors.role && (
                <Text style={styles.fieldError}>{errors.role}</Text>
                )}
              </TouchableOpacity>
              </>
            )}
            {showRolePicker && (
              <View style={styles.genderOptions}>
                {roleOptions.map((option) => (
                  <TouchableOpacity
                    key={option.value}
                    style={styles.genderOption}
                    onPress={() => {
                      handleInputChange("role", option.value);
                      setShowRolePicker(false);
                    }}
                  >
                    <Text style={styles.genderOptionText}>{option.label}</Text>
                  </TouchableOpacity>
                ))}
              </View>
            )}

            <SimpleInput
              label="Password"
              placeholder="Create a password"
              value={formData.password}
              onChangeText={handlePasswordChange}
              secureTextEntry
              error={errors.password}
            />
            
            <SimpleInput
              label="Confirm Password"
              placeholder="Confirm your password"
              value={formData.confirmPassword}
              onChangeText={handleConfirmPasswordChange}
              secureTextEntry
              error={errors.confirmPassword}
            />

            <Button
              title="Create Account"
              onPress={handleRegister}
              loading={isLoading}
              fullWidth
              style={styles.button}
            />
          </View>
          
          <View style={styles.footer}>
            <Text style={styles.footerText}>Already have an account? </Text>
            <TouchableOpacity onPress={() => router.push("/auth/login")}>
              <Text style={styles.footerLink}>Sign In</Text>
            </TouchableOpacity>
          </View>
        </ScrollView>
      </LinearGradient>
    </KeyboardAvoidingView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollContent: {
    flexGrow: 1,
    padding: 20,
    paddingTop: 60,
  },
  backButton: {
    position: "absolute",
    top: 50,
    left: 20,
    zIndex: 10,
  },
  header: {
    alignItems: "center",
    marginBottom: 30,
    marginTop: 40,
  },
  title: {
    fontSize: 24,
    fontWeight: "bold",
    color: designTokens.colors.semantic.text,
    marginTop: 20,
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    color: designTokens.colors.semantic.text,
  },
  form: {
    width: "100%",
    marginBottom: 20,
  },
  errorContainer: {
    backgroundColor: "#FFEBEE",
    padding: 12,
    borderRadius: 8,
    marginBottom: 16,
  },
  errorText: {
    color: designTokens.colors.semantic.error,
    fontSize: 14,
  },
  button: {
    marginTop: 10,
    width: "100%",
  },
  footer: {
    flexDirection: "row",
    justifyContent: "center",
    marginTop: "auto",
    paddingVertical: 20,
  },
  footerText: {
    color: designTokens.colors.semantic.text,
    fontSize: 14,
  },
  footerLink: {
    color: designTokens.colors.semantic.primary,
    fontSize: 14,
    fontWeight: "500",
  },
  logo: {
    width: 100,
    height: 100,
  },
  datePickerButton: {
    backgroundColor: "rgba(255, 255, 255, 0.9)",
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    borderWidth: 1,
    borderColor: "rgba(255, 255, 255, 0.3)",
  },
  datePickerContent: {
    flexDirection: "column",
  },
  datePickerLabel: {
    color: designTokens.colors.semantic.text,
    marginBottom: designTokens.spacing.scale.sm,
    fontWeight: designTokens.typography.weights.medium,
    fontSize: designTokens.typography.sizes.small,
  },
  datePickerValue: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },
  datePickerText: {
    fontSize: 16,
    color: designTokens.colors.semantic.text,
  },
  genderPickerButton: {
    backgroundColor: "rgba(255, 255, 255, 0.9)",
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    borderWidth: 1,
    borderColor: "rgba(255, 255, 255, 0.3)",
  },
  genderPickerContent: {
    flexDirection: "column",
  },
  genderPickerLabel: {
    color: designTokens.colors.semantic.text,
    marginBottom: designTokens.spacing.scale.sm,
    fontWeight: designTokens.typography.weights.medium,
    fontSize: designTokens.typography.sizes.small,
  },
  genderPickerValue: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },
  genderPickerText: {
    fontSize: 16,
    color: designTokens.colors.semantic.text,
  },
  genderOptions: {
    backgroundColor: "rgba(255, 255, 255, 0.95)",
    borderRadius: 12,
    marginBottom: 16,
    borderWidth: 1,
    borderColor: "rgba(255, 255, 255, 0.3)",
  },
  genderOption: {
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: "rgba(0, 0, 0, 0.1)",
  },
  genderOptionText: {
    fontSize: 16,
    color: designTokens.colors.semantic.text,
  },
  fieldError: {
    color: designTokens.colors.semantic.error,
    fontSize: 12,
    marginTop: 4,
  },
  datePickerModal: {
    backgroundColor: "rgba(255, 255, 255, 0.95)",
    borderRadius: 12,
    marginBottom: 16,
    borderWidth: 1,
    borderColor: "rgba(255, 255, 255, 0.3)",
  },
  datePickerHeader: {
    flexDirection: "row",
    justifyContent: "flex-end",
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: "rgba(0, 0, 0, 0.1)",
  },
  datePickerDoneButton: {
    color: designTokens.colors.semantic.primary,
    fontSize: 16,
    fontWeight: "600",
  },
  datePickerIOS: {
    backgroundColor: "transparent",
  },
});
