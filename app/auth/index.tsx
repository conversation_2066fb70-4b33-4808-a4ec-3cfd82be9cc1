import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity, ScrollView, Image } from 'react-native';
import { router } from 'expo-router';
import { Logo } from '@/components/ui/Logo';
import { Button } from '@/components/ui/Button';
import { Card } from '@/components/ui/Card';
import { RadialGradient } from '@/components/ui/RadialGradient';
import { designTokens } from '@/constants/design-tokens';
import { User, Briefcase, Zap } from 'lucide-react-native';
import { useAuthStoreHydrated } from '@/hooks/useAuthStoreHydrated';

export default function AuthScreen() {
  const { demoLogin, isLoading } = useAuthStoreHydrated();

  const handleLogin = () => {
    router.push('/auth/login');
  };

  const handleCustomerSignup = () => {
    router.push('/auth/register?role=customer');
  };

  const handleCompanionSignup = () => {
    router.push({
      pathname: '/auth/register',
      params: {
        role: 'companion'
      }
    });
  };


  return (
    <RadialGradient variant="primary" style={styles.container}>
      <ScrollView contentContainerStyle={styles.scrollContent}>
        <View style={styles.logoContainer}>
          <Image source={require('@/assets/images/tirak-logo.png')} style={styles.logo} />
          <Text style={styles.tagline}>Discover Thailand with trusted local companions</Text>
        </View>

        <View style={styles.buttonContainer}>
          <Card style={styles.buttonCard} padding={20}>
            <TouchableOpacity
              style={styles.roleButton}
              onPress={handleCustomerSignup}
            >
              <View style={styles.roleIcon}>
                <User size={24} color={designTokens.colors.semantic.primary} />
              </View>
              <View style={styles.roleContent}>
                <Text style={styles.roleTitle}>Explore as Traveler</Text>
                <Text style={styles.roleDescription}>Book authentic Thai experiences with local companions</Text>
              </View>
            </TouchableOpacity>
          </Card>

          <Card style={styles.buttonCard} padding={20}>
            <TouchableOpacity
              style={styles.roleButton}
              onPress={handleCompanionSignup}
            >
              <View style={styles.roleIcon}>
                <Briefcase size={24} color={designTokens.colors.semantic.primary} />
              </View>
              <View style={styles.roleContent}>
                <Text style={styles.roleTitle}>Become a Companion/Supplier</Text>
                <Text style={styles.roleDescription}>Share your Thailand expertise and earn income</Text>
              </View>
            </TouchableOpacity>
          </Card>

          <Button
            title="Already have an account? Login"
            variant="outline"
            onPress={handleLogin}
            fullWidth
            style={styles.loginButton}
          />


          
        </View>

        <View style={styles.footer}>
          <Text style={styles.footerText}>
            By continuing, you agree to our
          </Text>
          <View style={styles.footerLinks}>
            <TouchableOpacity>
              <Text style={styles.link}>Terms of Service</Text>
            </TouchableOpacity>
            <Text style={styles.footerText}> and </Text>
            <TouchableOpacity>
              <Text style={styles.link}>Privacy Policy</Text>
            </TouchableOpacity>
          </View>
        </View>
      </ScrollView>
    </RadialGradient>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollContent: {
    flexGrow: 1,
    justifyContent: 'space-between',
    padding: 20,
    paddingTop: 80,
    paddingBottom: 40,
  },
  logoContainer: {
    alignItems: 'center',
    marginBottom: 60,
  },
  tagline: {
    fontSize: 16,
    color: designTokens.colors.semantic.text,
    textAlign: 'center',
    fontWeight: '500',
    marginTop: 20,
    opacity: 0.9,
  },
  buttonContainer: {
    width: '100%',
    gap: 16,
  },
  buttonCard: {
    marginBottom: 8,
  },
  roleButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 8,
  },
  roleIcon: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: 'rgba(111, 76, 170, 0.1)',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  roleContent: {
    flex: 1,
  },
  roleTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: designTokens.colors.semantic.text,
    marginBottom: 4,
  },
  roleDescription: {
    fontSize: 14,
    color: designTokens.colors.semantic.textSecondary,
  },
  loginButton: {
    marginTop: 16,
  },
  demoSection: {
    marginTop: 24,
    paddingTop: 20,
    borderTopWidth: 1,
    borderTopColor: 'rgba(255, 255, 255, 0.2)',
  },
  demoHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 16,
  },
  demoTitle: {
    color: designTokens.colors.semantic.text,
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 8,
  },
  demoButtons: {
    flexDirection: 'row',
    gap: 12,
  },
  demoButton: {
    flex: 1,
  },
  footer: {
    alignItems: 'center',
    marginTop: 20,
  },
  footerText: {
    color: designTokens.colors.semantic.text,
    fontSize: 14,
    opacity: 0.8,
  },
  footerLinks: {
    flexDirection: 'row',
    marginTop: 4,
  },
  link: {
    color: designTokens.colors.semantic.text,
    fontSize: 14,
    fontWeight: '600',
    textDecorationLine: 'underline',
  },
  logo: {
    width: 100,
    height: 100,
  },
});