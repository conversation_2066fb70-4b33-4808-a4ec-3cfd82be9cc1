import React, { useState } from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity, Dimensions, ViewStyle, TextStyle, ImageStyle } from 'react-native';
import { Image } from 'expo-image';
import { useLocalSearchParams, router, Link } from 'expo-router';
import { Card } from '@/components/ui/Card';
import { ProfileImage } from '@/components/ui/ProfileImage';
import { CategoryChip } from '@/components/ui/CategoryChip';
import { RadialGradient } from '@/components/ui/RadialGradient';
import { Button } from '@/components/ui/Button';
import { ImageCarousel } from '@/components/ui/ImageCarousel';
import { LoadingState } from '@/components/ui/LoadingState';
import CompanionProfileShimmer from '@/components/ui/CompanionProfileShimmer';
import { designTokens, componentTokens } from '@/constants/design-tokens';
import { useBookingStore } from '@/stores/booking-store';
import { useNavigation } from '@react-navigation/native';
import { BookingService } from '@/types/booking';
import { useToastStore } from '@/stores/toast-store';

// Import new API functions
import { 
  useCompanionQuery, 
  useCompanionWeeklyAvailability,
  CompanionDetails 
} from '@/app/api/companion/companion';

import { 
  ArrowLeft, 
  Heart, 
  Share, 
  Star, 
  MessageCircle, 
  Calendar, 
  MapPin, 
  Globe, 
  Shield, 
  ChevronRight,
  ChevronDown,
  ChevronUp,
} from 'lucide-react-native';

const { width } = Dimensions.get('window');

// Simple shimmer for inline loading states
const ShimmerBox = ({ width: w, height: h, style }: { width: number | string; height: number; style?: ViewStyle }) => (
  <View style={[{ width: w as any, height: h, backgroundColor: '#E1E9EE', borderRadius: 8 }, style]} />
);

// Helper to map API response to UI structure
function mapCompanionDetails(data: any) {
  return {
    id: data.id,
    name: data.name,
    displayName: data.displayName,
    profileImage: data.profileImage,
    gallery: data.gallery,
    location: data.location,
    rating: data.rating,
    reviewCount: data.reviewCount,
    price: data.price,
    experiences: Array.isArray(data.experiences) ? data.experiences : [],
    languages: data.languages,
    verified: data.verified,
    online: data.online,
    lastSeen: data.lastSeen,
    categories: data.categories,
    bio: data.bio,
    age: data.age,
    responseTime: data.responseTime,
    completionRate: data.completionRate,
    joinedDate: data.joinedDate,
    availability: data.availability,
    reviews: data.reviews,
    firstName: data.firstName,
    lastName: data.lastName,
    dateOfBirth: data.dateOfBirth,
    gender: data.gender,
    socialLinks: data.socialLinks,
    specialization: data.specialization,
    certifications: data.certifications,
  };
}

export default function CompanionProfileScreen() {
  const { id } = useLocalSearchParams();
  const [isFavorite, setIsFavorite] = useState(false);
  const [selectedTab, setSelectedTab] = useState<'about' | 'services' | 'reviews'>('about');
  const [showAllServices, setShowAllServices] = useState(false);
  const [showAllLanguages, setShowAllLanguages] = useState(false);
  const [selectedDate, setSelectedDate] = useState<string | null>(null);
  
  // API calls
  const { data: companionData, isLoading: isLoadingCompanion, error: companionError } = useCompanionQuery(id as string);
  const { data: availabilityData, isLoading: isLoadingAvailability } = useCompanionWeeklyAvailability(id as string, '00:00', '23:59');
  
  const navigation = useNavigation();
  const { setCompanionData } = useBookingStore();
  const { showToast } = useToastStore();
  
  // Show loading state
  if (isLoadingCompanion) {
    return <CompanionProfileShimmer />;
  }
  
  // Show error state
  if (companionError || !companionData?.success) {
    return (
      <View style={[styles.container, { justifyContent: 'center', alignItems: 'center', padding: 20 }]}>
        <Text style={styles.sectionTitle}>Error loading companion</Text>
        <Text style={styles.bioText}>
          {companionError?.message || 'Failed to load companion details'}
        </Text>
        <Button
          title="Go Back"
          variant="primary"
          onPress={() => router.back()}
          style={{ marginTop: 20 }}
        />
      </View>
    );
  }
  
  const companion = mapCompanionDetails(companionData.data);
  
  
  const handleBack = () => {
    router.back();
  };
  
  const toggleFavorite = () => {
    setIsFavorite(!isFavorite);
  };
  
  const handleShare = () => {
    // Share functionality would go here
  };
  
  const handleChat = () => {
    router.push(`/chat/${companion.id}`);
  };
  
  const handleBookNow = () => {
    if (!selectedDate) {
      showToast({
        message: 'Please select a date first',
        type: 'warning'
      });
      return;
    }

    if (companion && companion.experiences && companion.experiences.length > 0) {
      const defaultService = companion.experiences[0];
      const defaultDuration = defaultService.durationMinutes || 60;

      // Set companion data in the store
      setCompanionData({
        id: companion.id,
        name: companion.name,
        image: companion.profileImage,
        location: companion.location,
        rating: companion.rating,
        languages: companion.languages || [],
        about: companion.bio,
        specialties: companion.experiences.map((exp: BookingService) => exp.name),
      });

      // Update service in the store
      useBookingStore.getState().updateService({
        id: defaultService.id,
        name: defaultService.title,
        description: defaultService.description,
        price: defaultService.price,
        duration: defaultDuration / 60, // Convert to hours
        category: defaultService.category || 'Experience',
      });

      // Update date/time in the store
      useBookingStore.getState().updateDateTime({
        date: selectedDate,
        time: '10:00', // Default to 10 AM
        duration: defaultDuration / 60, // Convert minutes to hours
        endTime: '11:00', // Will be calculated properly in the datetime step
        isAvailable: true
      });

      // Reset to first step and navigate
      useBookingStore.getState().goToStep(1);
      router.push('/booking/new');
    } else {
      showToast({
        message: 'No services available for this companion',
        type: 'error'
      });
    }
  };
  
  // Transform availability data or use mock data if not available
  const availableDates = availabilityData?.success && availabilityData.data.availability 
    ? availabilityData.data.availability.map(day => ({
        date: day.date,
        day: new Date(day.date).getDate(),
        weekday: new Date(day.date).toLocaleDateString('en', { weekday: 'short' }),
        available: day.available,
      }))
    : Array.from({ length: 14 }, (_, i) => {
        const date = new Date();
        date.setDate(date.getDate() + i + 1);
        return {
          date: date.toISOString().split('T')[0],
          day: date.getDate(),
          weekday: date.toLocaleDateString('en', { weekday: 'short' }),
          available: Math.random() > 0.3,
        };
      });
  
  const renderDateItem = (item: any, index: number) => (
    <TouchableOpacity
      key={index}
      style={[
        styles.dateItem as ViewStyle,
        !item.available && styles.dateItemUnavailable as ViewStyle,
        selectedDate === item.date && styles.dateItemSelected as ViewStyle,
      ]}
      onPress={() => item.available && setSelectedDate(item.date)}
      disabled={!item.available}
    >
      <Text style={[
        styles.dateDay as TextStyle,
        !item.available && styles.dateTextUnavailable as TextStyle,
        selectedDate === item.date && styles.dateTextSelected as TextStyle,
      ]}>
        {item.day}
      </Text>
      <Text style={[
        styles.dateWeekday as TextStyle,
        !item.available && styles.dateTextUnavailable as TextStyle,
        selectedDate === item.date && styles.dateTextSelected as TextStyle,
      ]}>
        {item.weekday}
      </Text>
    </TouchableOpacity>
  );
  
  const renderReviewItem = (review: any) => (
    <Card key={review.id} style={styles.reviewCard as ViewStyle} padding={16}>
      <View style={styles.reviewHeader as ViewStyle}>
        <View style={styles.reviewUser as ViewStyle}>
          <Image
            source={{ uri: review.user.profileImage || 'https://via.placeholder.com/40' }}
            style={styles.reviewAvatar as ImageStyle}
            contentFit="cover"
          />
          <View>
            <Text style={styles.reviewUserName as TextStyle}>{review.user.name}</Text>
            <Text style={styles.reviewDate as TextStyle}>{review.date}</Text>
          </View>
        </View>
        <View style={styles.reviewRating as ViewStyle}>
          {Array.from({ length: review.rating }, (_, i) => (
            <Star key={i} size={14} color="#FFD700" fill="#FFD700" />
          ))}
        </View>
      </View>
      <Text style={styles.reviewText as TextStyle}>{review.comment}</Text>
    </Card>
  );
  
  // Prepare gallery images
  const galleryImages = companion.gallery && companion.gallery.length > 0 
    ? companion.gallery 
    : [companion.profileImage];

  return (
    <View style={styles.container as ViewStyle}>
      {/* Enhanced Header with Image Carousel */}
      <View style={styles.header as ViewStyle}>
        <ImageCarousel
          images={galleryImages}
          height={300}
          showDots={true}
          enableFullScreen={true}
          showImageCounter={true}
          style={styles.carousel}
        />
        
        <View style={styles.headerOverlay as ViewStyle}>
          <TouchableOpacity style={styles.backButton as ViewStyle} onPress={handleBack}>
            <ArrowLeft size={24} color={designTokens.colors.semantic.surface} />
          </TouchableOpacity>

          <View style={styles.headerActions as ViewStyle}>
            <TouchableOpacity
              style={styles.actionButton as ViewStyle}
              onPress={toggleFavorite}
            >
              <Heart
                size={24}
                color={designTokens.colors.semantic.surface}
                fill={isFavorite ? designTokens.colors.semantic.accent : 'transparent'}
              />
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.actionButton as ViewStyle}
              onPress={handleShare}
            >
              <Share size={24} color={designTokens.colors.semantic.surface} />
            </TouchableOpacity>
          </View>
        </View>
      </View>
      
      <ScrollView style={styles.content as ViewStyle} showsVerticalScrollIndicator={false}>
        {/* Profile Info */}
        <Card style={styles.profileCard as ViewStyle} padding={20}>
          <View style={styles.profileHeader as ViewStyle}>
            <View style={styles.profileInfo as ViewStyle}>
              <Text style={styles.profileName as TextStyle}>{companion.displayName || companion.name}</Text>
              <View style={styles.profileMeta as ViewStyle}>
                <View style={styles.ratingContainer as ViewStyle}>
                  <Star size={16} color="#FFD700" fill="#FFD700" />
                  <Text style={styles.ratingText as TextStyle}>
                    {companion.rating} ({companion.reviewCount} reviews)
                  </Text>
                </View>
                
                <View style={styles.locationContainer as ViewStyle}>
                  <MapPin size={16} color={designTokens.colors.semantic.textSecondary} />
                  <Text style={styles.locationText as TextStyle}>{companion.location}</Text>
                </View>
              </View>
            </View>
            
            <ProfileImage
              uri={companion.profileImage}
              size="large"
              online={companion.online}
            />
          </View>
          
          {companion.verified && (
            <View style={styles.verificationBadge as ViewStyle}>
              <Shield size={16} color={designTokens.colors.semantic.success} />
              <Text style={styles.verificationText as TextStyle}>Verified Profile</Text>
            </View>
          )}
          
          <View style={styles.priceContainer as ViewStyle}>
            <Text style={styles.price as TextStyle}>฿{companion.price.toLocaleString()}</Text>
            <Text style={styles.priceUnit as TextStyle}>/day</Text>
          </View>
          
          <View style={styles.actionButtons as ViewStyle}>
            <Button
              title="Chat Now"
              variant="primary"
              onPress={handleChat}
              style={styles.chatButton as ViewStyle}
              leftIcon={<MessageCircle size={18} color={designTokens.colors.semantic.surface} />}
            />
          </View>
        </Card>
        
        {/* Tabs */}
        <Card style={styles.tabsCard as ViewStyle} padding={0}>
          <View style={styles.tabsHeader as ViewStyle}>
            <TouchableOpacity
              style={[styles.tab as ViewStyle, selectedTab === 'about' && styles.tabActive as ViewStyle]}
              onPress={() => setSelectedTab('about')}
            >
              <Text style={[styles.tabText as TextStyle, selectedTab === 'about' && styles.tabTextActive as TextStyle]}>
                About
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[styles.tab as ViewStyle, selectedTab === 'services' && styles.tabActive as ViewStyle]}
              onPress={() => setSelectedTab('services')}
            >
              <Text style={[styles.tabText as TextStyle, selectedTab === 'services' && styles.tabTextActive as TextStyle]}>
                Services
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[styles.tab as ViewStyle, selectedTab === 'reviews' && styles.tabActive as ViewStyle]}
              onPress={() => setSelectedTab('reviews')}
            >
              <Text style={[styles.tabText as TextStyle, selectedTab === 'reviews' && styles.tabTextActive as TextStyle]}>
                Reviews
              </Text>
            </TouchableOpacity>
          </View>

          <View style={styles.tabContent as ViewStyle}>
            {selectedTab === 'about' && (
              <View style={styles.aboutContent as ViewStyle}>
                <View style={styles.bioSection as ViewStyle}>
                  <Text style={styles.sectionTitle as TextStyle}>Bio</Text>
                  <Text style={styles.bioText as TextStyle}>{companion.bio}</Text>
                </View>

                <View style={styles.languagesSection as ViewStyle}>
                  <Text style={styles.sectionTitle as TextStyle}>Languages</Text>
                  <View style={styles.languagesList as ViewStyle}>
                    {companion.languages.slice(0, showAllLanguages ? companion.languages.length : 3).map((language: string, index: number) => (
                      <View key={index} style={styles.languageItem as ViewStyle}>
                        <Globe size={16} color={designTokens.colors.semantic.primary} />
                        <Text style={styles.languageText as TextStyle}>{language}</Text>
                      </View>
                    ))}
                  </View>

                  {companion.languages.length > 3 && (
                    <TouchableOpacity
                      style={styles.showMoreButton as ViewStyle}
                      onPress={() => setShowAllLanguages(!showAllLanguages)}
                    >
                      <Text style={styles.showMoreText as TextStyle}>
                        {showAllLanguages ? 'Show Less' : 'Show All Languages'}
                      </Text>
                      {showAllLanguages ? (
                        <ChevronUp size={16} color={designTokens.colors.semantic.primary} />
                      ) : (
                        <ChevronDown size={16} color={designTokens.colors.semantic.primary} />
                      )}
                    </TouchableOpacity>
                  )}
                </View>
              </View>
            )}

            {selectedTab === 'services' && (
              <View style={styles.servicesContent as ViewStyle}>
                <Text style={styles.sectionTitle as TextStyle}>Experiences Offered</Text>
                <View style={styles.servicesList as ViewStyle}>
                  {companion.experiences.slice(0, showAllServices ? companion.experiences.length : 4).map((exp: any, index: number) => (
                    <View key={exp.id || index} style={[styles.serviceItem as ViewStyle, { alignItems: 'flex-start' }]}> 
                        <View style={styles.serviceIcon as ViewStyle}>
                          <Text style={styles.serviceEmoji as TextStyle}>✓</Text>
                        </View>
                      <View style={{ flex: 1, minWidth: 0 }}>
                        <Text
                          style={[styles.serviceText as TextStyle, { flexWrap: 'wrap' }]}
                          numberOfLines={3}
                          ellipsizeMode="tail"
                        >
                          {exp.title} - {exp.description} ({exp.durationMinutes} min, ฿{exp.price})
                        </Text>
                      </View>
                    </View>
                  ))}
                </View>

                {companion.experiences.length > 4 && (
                  <TouchableOpacity
                    style={styles.showMoreButton as ViewStyle}
                    onPress={() => setShowAllServices(!showAllServices)}
                  >
                    <Text style={styles.showMoreText as TextStyle}>
                      {showAllServices ? 'Show Less' : 'Show All Experiences'}
                    </Text>
                    {showAllServices ? (
                      <ChevronUp size={16} color={designTokens.colors.semantic.primary} />
                    ) : (
                      <ChevronDown size={16} color={designTokens.colors.semantic.primary} />
                    )}
                  </TouchableOpacity>
                )}
              </View>
            )}

            {selectedTab === 'reviews' && (
              <View style={styles.reviewsContent as ViewStyle}>
                <View style={styles.reviewsHeader as ViewStyle}>
                  <Text style={styles.sectionTitle as TextStyle}>Customer Reviews</Text>
                  <View style={styles.overallRating as ViewStyle}>
                    <Star size={18} color="#FFD700" fill="#FFD700" />
                    <Text style={styles.overallRatingText as TextStyle}>{companion.rating}</Text>
                    <Text style={styles.reviewCount as TextStyle}>({companion.reviewCount} reviews)</Text>
                  </View>
                </View>

                <View style={styles.reviewsList as ViewStyle}>
                  {companion.reviews.map(renderReviewItem)}
                </View>

                <TouchableOpacity style={styles.allReviewsButton as ViewStyle}>
                  <Text style={styles.allReviewsText as TextStyle}>See All Reviews</Text>
                  <ChevronRight size={16} color={designTokens.colors.semantic.primary} />
                </TouchableOpacity>
              </View>
            )}
          </View>
        </Card>

        {/* Availability */}
        <Card style={styles.availabilityCard as ViewStyle} padding={20}>
          <Text style={styles.sectionTitle as TextStyle}>Availability</Text>
          <Text style={styles.availabilitySubtitle as TextStyle}>Select a date to book</Text>
          
          {isLoadingAvailability ? (
            <View style={{ flexDirection: 'row', gap: 12, paddingVertical: 12 }}>
              {Array.from({ length: 7 }).map((_, i) => (
                <ShimmerBox key={i} width={60} height={80} style={{ borderRadius: 12 }} />
              ))}
            </View>
          ) : (
            <ScrollView
              horizontal
              showsHorizontalScrollIndicator={false}
              contentContainerStyle={styles.datesContainer as ViewStyle}
            >
              {availableDates.map(renderDateItem)}
            </ScrollView>
          )}

          {/* <TouchableOpacity style={styles.calendarButton as ViewStyle}>
            <Text style={styles.calendarButtonText as TextStyle}>View Full Calendar</Text>
            <Calendar size={16} color={designTokens.colors.semantic.primary} />
          </TouchableOpacity> */}

          {/* Conditional Book Now Button */}
          {selectedDate && (
            <Button
              title="Book Now"
              variant="primary"
              onPress={handleBookNow}
              style={styles.bookNowButton as ViewStyle}
              leftIcon={<Calendar size={18} color={designTokens.colors.semantic.surface} />}
              fullWidth
            />
          )}
        </Card>
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: designTokens.colors.semantic.background,
  },
  header: {
    height: 300,
    position: 'relative',
  },
  carousel: {
    width: '100%',
    height: '100%',
  },
  headerOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    flexDirection: 'row',
    justifyContent: 'space-between',
    padding: designTokens.spacing.scale.lg,
    paddingTop: 50,
    zIndex: 10,
  },
  backButton: {
    width: 44,
    height: 44,
    borderRadius: 22,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    ...designTokens.shadows.sm,
  },
  headerActions: {
    flexDirection: 'row',
    gap: designTokens.spacing.scale.sm,
  },
  actionButton: {
    width: 44,
    height: 44,
    borderRadius: 22,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    ...designTokens.shadows.sm,
  },
  content: {
    flex: 1,
    marginTop: -30,
    borderTopLeftRadius: 30,
    borderTopRightRadius: 30,
    overflow: 'hidden',
  },
  profileCard: {
    marginHorizontal: designTokens.spacing.scale.lg,
    marginBottom: designTokens.spacing.scale.md,
    borderRadius: designTokens.borderRadius.components.card,
  },
  profileHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: designTokens.spacing.scale.md,
  },
  profileInfo: {
    flex: 1,
    marginRight: designTokens.spacing.scale.md,
  },
  profileName: {
    ...designTokens.typography.styles.heading,
    color: designTokens.colors.semantic.text,
    marginBottom: designTokens.spacing.scale.sm,
  },
  profileMeta: {
    gap: 8,
  },
  ratingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  ratingText: {
    fontSize: 14,
    color: designTokens.colors.semantic.text,
    marginLeft: 4,
  },
  locationContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  locationText: {
    fontSize: 14,
    color: designTokens.colors.semantic.textSecondary,
    marginLeft: 4,
  },
  verificationBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(76, 175, 80, 0.1)',
    paddingHorizontal: 10,
    paddingVertical: 6,
    borderRadius: 12,
    alignSelf: 'flex-start',
    marginBottom: 16,
  },
  verificationText: {
    fontSize: 12,
    color: designTokens.colors.semantic.success,
    fontWeight: '600',
    marginLeft: 4,
  },
  priceContainer: {
    flexDirection: 'row',
    alignItems: 'baseline',
    marginBottom: 16,
  },
  price: {
    fontSize: 24,
    fontWeight: 'bold',
    color: designTokens.colors.semantic.primary,
  },
  priceUnit: {
    fontSize: 14,
    color: designTokens.colors.semantic.textSecondary,
    marginLeft: 4,
  },
  actionButtons: {
    flexDirection: 'row',
    justifyContent: 'center',
    marginTop: designTokens.spacing.scale.md,
  },
  chatButton: {
    minWidth: 160,
    maxWidth: 200,
    height: 48,
    paddingHorizontal: designTokens.spacing.scale.lg,
  },
  tabsCard: {
    marginHorizontal: 20,
    marginBottom: 16,
    borderRadius: 20,
    overflow: 'hidden',
  },
  tabsHeader: {
    flexDirection: 'row',
    borderBottomWidth: 1,
    borderBottomColor: designTokens.colors.semantic.border,
  },
  tab: {
    flex: 1,
    paddingVertical: 16,
    alignItems: 'center',
  },
  tabActive: {
    borderBottomWidth: 2,
    borderBottomColor: designTokens.colors.semantic.primary,
  },
  tabText: {
    fontSize: 16,
    fontWeight: '500',
    color: designTokens.colors.semantic.textSecondary,
  },
  tabTextActive: {
    color: designTokens.colors.semantic.primary,
    fontWeight: '600',
  },
  tabContent: {
    padding: 20,
  },
  aboutContent: {
    gap: 20,
  },
  bioSection: {},
  sectionTitle: {
    ...designTokens.typography.styles.subheading,
    color: designTokens.colors.semantic.text,
    marginBottom: designTokens.spacing.scale.sm,
  },
  bioText: {
    fontSize: 16,
    color: designTokens.colors.semantic.textSecondary,
    lineHeight: 24,
  },
  languagesSection: {},
  languagesList: {
    gap: 8,
  },
  languageItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  languageText: {
    fontSize: 16,
    color: designTokens.colors.semantic.text,
  },
  showMoreButton: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 12,
    alignSelf: 'flex-start',
  },
  showMoreText: {
    fontSize: 14,
    color: designTokens.colors.semantic.primary,
    fontWeight: '500',
    marginRight: 4,
  },
  servicesContent: {},
  servicesList: {
    gap: 12,
  },
  serviceItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  serviceIcon: {
    width: 30,
    height: 30,
    borderRadius: 15,
    backgroundColor: 'rgba(111, 76, 170, 0.1)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  serviceEmoji: {
    fontSize: 16,
    color: designTokens.colors.semantic.primary,
  },
  serviceText: {
    fontSize: 16,
    color: designTokens.colors.semantic.text,
  },
  reviewsContent: {},
  reviewsHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  overallRating: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  overallRatingText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: designTokens.colors.semantic.text,
  },
  reviewCount: {
    fontSize: 14,
    color: designTokens.colors.semantic.textSecondary,
  },
  reviewsList: {
    gap: 12,
  },
  reviewCard: {
    marginBottom: 4,
  },
  reviewHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 12,
  },
  reviewUser: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  reviewAvatar: {
    width: 40,
    height: 40,
    borderRadius: 20,
  },
  reviewUserName: {
    fontSize: 16,
    fontWeight: '600',
    color: designTokens.colors.semantic.text,
  },
  reviewDate: {
    fontSize: 12,
    color: designTokens.colors.semantic.textSecondary,
  },
  reviewRating: {
    flexDirection: 'row',
    gap: 2,
  },
  reviewText: {
    fontSize: 14,
    color: designTokens.colors.semantic.text,
    lineHeight: 22,
  },
  allReviewsButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: 16,
  },
  allReviewsText: {
    fontSize: 16,
    color: designTokens.colors.semantic.primary,
    fontWeight: '500',
    marginRight: 4,
  },
  availabilityCard: {
    marginHorizontal: designTokens.spacing.scale.lg,
    marginBottom: designTokens.spacing.scale.xl,
    borderRadius: designTokens.borderRadius.components.card,
  },
  availabilitySubtitle: {
    ...designTokens.typography.styles.caption,
    color: designTokens.colors.semantic.textSecondary,
    marginBottom: designTokens.spacing.scale.md,
    marginTop: -designTokens.spacing.scale.sm,
  },
  datesContainer: {
    gap: designTokens.spacing.scale.sm,
    paddingVertical: designTokens.spacing.scale.sm,
  },
  dateItem: {
    width: 60,
    height: 80,
    borderRadius: designTokens.borderRadius.components.button,
    borderWidth: 1,
    borderColor: designTokens.colors.semantic.border,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: designTokens.colors.semantic.surface,
    ...designTokens.shadows.sm,
  },
  dateItemUnavailable: {
    backgroundColor: designTokens.colors.semantic.border,
    opacity: 0.6,
  },
  dateItemSelected: {
    backgroundColor: designTokens.colors.semantic.primary,
    borderColor: designTokens.colors.semantic.primary,
    ...designTokens.shadows.md,
  },
  dateDay: {
    fontSize: designTokens.typography.sizes.large,
    fontWeight: designTokens.typography.weights.bold as any,
    color: designTokens.colors.semantic.text,
    marginBottom: designTokens.spacing.scale.xs,
  },
  dateWeekday: {
    ...designTokens.typography.styles.caption,
    color: designTokens.colors.semantic.textSecondary,
    fontWeight: designTokens.typography.weights.medium as any,
  },
  dateTextUnavailable: {
    color: designTokens.colors.semantic.textSecondary,
  },
  dateTextSelected: {
    color: designTokens.colors.semantic.surface,
  },
  calendarButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: designTokens.spacing.scale.md,
    paddingVertical: designTokens.spacing.scale.sm,
  },
  calendarButtonText: {
    ...designTokens.typography.styles.body,
    color: designTokens.colors.semantic.primary,
    fontWeight: designTokens.typography.weights.medium as any,
    marginRight: designTokens.spacing.scale.sm,
  },
  bookNowButton: {
    marginTop: designTokens.spacing.scale.lg,
    borderRadius: designTokens.borderRadius.components.button,
  },
});
