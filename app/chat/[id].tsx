import React, { useState, useRef, useEffect } from 'react';
import {
  View,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  KeyboardAvoidingView,
  Platform,
  FlatList,
  Keyboard,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useLocalSearchParams, router } from 'expo-router';
import { mockCompanions } from '@/mocks/companions';
import { ProfileImage } from '@/components/ui/ProfileImage';
import { RadialGradient } from '@/components/ui/RadialGradient';
import { Body, Caption } from '@/components/ui/Typography';
import { designTokens, componentTokens } from '@/constants/design-tokens';
import {
  ArrowLeft,
  Phone,
  Video,
  MoreVertical,
  Send,
  Image as ImageIcon,
  Mic,
  Smile,
  Check,
  CheckCheck,
  Languages,
} from 'lucide-react-native';

// Import new chat components
import { ImageMessage } from '@/components/chat/ImageMessage';
import { VoiceMessage } from '@/components/chat/VoiceMessage';
import { TranslationToggle } from '@/components/chat/TranslationToggle';
import { ChatSettings } from '@/components/chat/ChatSettings';
import { EnhancedMessageInput } from '@/components/chat/EnhancedMessageInput';

// TypeScript interfaces for type safety
interface Message {
  id: string;
  text?: string;
  sender: 'user' | 'companion';
  timestamp: string;
  status: 'sent' | 'delivered' | 'read';
  type: 'text' | 'image' | 'voice' | 'location' | 'booking';
  images?: string[];
  voiceDuration?: number;
  audioUrl?: string;
}

interface ChatHeaderProps {
  companion: any; // Use proper companion type from mocks
  onBack: () => void;
  onCall: () => void;
  onVideoCall: () => void;
  onMore: () => void;
}

interface MessageBubbleProps {
  message: Message;
  isUser: boolean;
}

interface TypingIndicatorProps {
  companion: any;
  visible: boolean;
}

interface MessageInputProps {
  message: string;
  onMessageChange: (text: string) => void;
  onSend: () => void;
  disabled?: boolean;
}

// Mock messages data with enhanced types
const mockMessages: Message[] = [
  {
    id: '1',
    text: 'Hello! I saw your profile and I would like to book you as a guide for my trip to Bangkok next week.',
    sender: 'user',
    timestamp: '10:30 AM',
    status: 'read',
    type: 'text',
  },
  {
    id: '2',
    text: 'Hi there! Thank you for reaching out. I would be happy to be your guide in Bangkok. When exactly are you planning to visit?',
    sender: 'companion',
    timestamp: '10:32 AM',
    status: 'read',
    type: 'text',
  },
  {
    id: '3',
    sender: 'companion',
    timestamp: '10:33 AM',
    status: 'read',
    type: 'image',
    images: [
      'https://images.unsplash.com/photo-1563492065-1a83e8c2b2e8?q=80&w=1000&auto=format&fit=crop',
      'https://images.unsplash.com/photo-1552550049-db097c9480d1?q=80&w=1000&auto=format&fit=crop',
      'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?q=80&w=1000&auto=format&fit=crop',
    ],
  },
  {
    id: '4',
    text: "I'll be arriving on Monday, June 15th and staying for 5 days. I'm particularly interested in exploring the temples and local food scene.",
    sender: 'user',
    timestamp: '10:35 AM',
    status: 'read',
    type: 'text',
  },
  {
    id: '5',
    sender: 'user',
    timestamp: '10:36 AM',
    status: 'read',
    type: 'voice',
    voiceDuration: 15,
    audioUrl: 'mock-audio-url',
  },
  {
    id: '6',
    text: "That sounds great! I specialize in cultural tours and know some amazing local food spots that tourists don't usually find. I'm available on the 15th and 16th. Would you like to book those days?",
    sender: 'companion',
    timestamp: '10:38 AM',
    status: 'read',
    type: 'text',
  },
  {
    id: '7',
    text: "Perfect! Yes, let's book for those two days. What time would you suggest we start?",
    sender: 'user',
    timestamp: '10:40 AM',
    status: 'delivered',
    type: 'text',
  },
];

// Component: Typing Indicator with organic motion
const TypingIndicator: React.FC<TypingIndicatorProps> = ({ companion, visible }) => {
  if (!visible) return null;

  return (
    <View style={styles.typingContainer}>
      <ProfileImage
        uri={companion.image}
        size="small"
      />
      <View style={styles.typingBubble}>
        <View style={styles.typingDots}>
          <View style={[styles.typingDot, styles.typingDot1]} />
          <View style={[styles.typingDot, styles.typingDot2]} />
          <View style={[styles.typingDot, styles.typingDot3]} />
        </View>
      </View>
    </View>
  );
};

// Component: Message Input with design tokens
const MessageInput: React.FC<MessageInputProps> = ({ message, onMessageChange, onSend, disabled }) => (
  <View style={styles.inputContainer}>
    <View style={styles.inputActions}>
      <TouchableOpacity
        style={styles.inputActionButton}
        accessibilityRole="button"
        accessibilityLabel="Add emoji"
      >
        <Smile size={24} color={designTokens.colors.semantic.textSecondary} />
      </TouchableOpacity>

      <TouchableOpacity
        style={styles.inputActionButton}
        accessibilityRole="button"
        accessibilityLabel="Add image"
      >
        <ImageIcon size={24} color={designTokens.colors.semantic.textSecondary} />
      </TouchableOpacity>

      <TouchableOpacity
        style={styles.inputActionButton}
        accessibilityRole="button"
        accessibilityLabel="Record voice message"
      >
        <Mic size={24} color={designTokens.colors.semantic.textSecondary} />
      </TouchableOpacity>
    </View>

    <View style={styles.textInputContainer}>
      <TextInput
        style={styles.textInput}
        placeholder="Type a message..."
        placeholderTextColor={designTokens.colors.semantic.textSecondary}
        value={message}
        onChangeText={onMessageChange}
        multiline
        accessibilityLabel="Message input"
        accessibilityHint="Type your message here"
      />

      <TouchableOpacity
        style={[
          styles.sendButton,
          disabled ? styles.sendButtonDisabled : styles.sendButtonActive
        ]}
        onPress={onSend}
        disabled={disabled}
        accessibilityRole="button"
        accessibilityLabel="Send message"
      >
        <Send size={20} color={disabled ? designTokens.colors.semantic.textSecondary : designTokens.colors.components.button.text} />
      </TouchableOpacity>
    </View>
  </View>
);

export default function ChatScreen() {
  const { id } = useLocalSearchParams();
  const [message, setMessage] = useState('');
  const [messages, setMessages] = useState(mockMessages);
  const [isTyping, setIsTyping] = useState(false);
  const [showTranslation, setShowTranslation] = useState(false);
  const [translationEnabled, setTranslationEnabled] = useState(false);
  const [showChatSettings, setShowChatSettings] = useState(false);
  const [isRecording, setIsRecording] = useState(false);
  const [recordingDuration, setRecordingDuration] = useState(0);
  const flatListRef = useRef<FlatList>(null);
  
  // Find companion by ID
  const companion = mockCompanions.find(c => c.id === id) || mockCompanions[0];
  
  useEffect(() => {
    // Scroll to bottom when messages change
    if (flatListRef.current) {
      flatListRef.current.scrollToEnd({ animated: true });
    }
  }, [messages]);
  
  const handleBack = () => {
    router.back();
  };
  
  const handleSend = () => {
    if (message.trim() === '') return;

    const newMessage: Message = {
      id: Date.now().toString(),
      text: message,
      sender: 'user',
      timestamp: new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }),
      status: 'sent',
      type: 'text',
    };

    setMessages([...messages, newMessage]);
    setMessage('');

    // Simulate companion typing
    setIsTyping(true);

    // Simulate companion response after a delay
    setTimeout(() => {
      setIsTyping(false);

      const responseMessage: Message = {
        id: (Date.now() + 1).toString(),
        text: "Thank you for your message! I'll get back to you shortly.",
        sender: 'companion',
        timestamp: new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }),
        status: 'read',
        type: 'text',
      };

      setMessages(prev => [...prev, responseMessage]);
    }, 3000);
  };

  const handleImageSelect = () => {
    // In a real app, this would open image picker
    // console.log('Opening image picker...'); 

    // Simulate adding an image message
    const imageMessage: Message = {
      id: Date.now().toString(),
      sender: 'user',
      timestamp: new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }),
      status: 'sent',
      type: 'image',
      images: ['https://images.unsplash.com/photo-1552550049-db097c9480d1?q=80&w=1000&auto=format&fit=crop'],
    };

    setMessages(prev => [...prev, imageMessage]);
  };

  const handleVoiceRecord = (recording: boolean) => {
    setIsRecording(recording);

    if (recording) {
      // Start recording timer
      const interval = setInterval(() => {
        setRecordingDuration(prev => prev + 1);
      }, 1000);

      // Store interval reference for cleanup
      setTimeout(() => {
        clearInterval(interval);
        if (isRecording) {
          handleVoiceRecord(false);
        }
      }, 60000); // Max 60 seconds
    } else {
      // Stop recording and add voice message
      if (recordingDuration > 0) {
        const voiceMessage: Message = {
          id: Date.now().toString(),
          sender: 'user',
          timestamp: new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }),
          status: 'sent',
          type: 'voice',
          voiceDuration: recordingDuration,
          audioUrl: 'mock-audio-url',
        };

        setMessages(prev => [...prev, voiceMessage]);
      }

      setRecordingDuration(0);
    }
  };

  const handleEmojiPress = () => {
    // In a real app, this would open emoji picker
    // console.log('Opening emoji picker...');
  };

  const handleTranslationToggle = (enabled: boolean) => {
    setTranslationEnabled(enabled);
    // console.log('Translation:', enabled ? 'enabled' : 'disabled');
  };

  const handleLanguageSelect = (source: string, target: string) => {
    // console.log('Language selection:', source, 'to', target);
  };
  
  // Component: Message Bubble with design tokens
  const MessageBubble: React.FC<MessageBubbleProps> = ({ message, isUser }) => (
    <View style={[styles.messageContainer, isUser ? styles.userMessageContainer : styles.companionMessageContainer]}>
      <View style={[styles.messageBubble, isUser ? styles.userMessageBubble : styles.companionMessageBubble]}>
        <Body style={[styles.messageText, isUser ? styles.userMessageText : styles.companionMessageText]}>
          {message.text}
        </Body>
      </View>

      <View style={[styles.messageFooter, isUser ? styles.userMessageFooter : styles.companionMessageFooter]}>
        <Caption style={styles.messageTime}>{message.timestamp}</Caption>

        {isUser && (
          <View style={styles.messageStatus}>
            {message.status === 'sent' && <Check size={14} color={designTokens.colors.semantic.textSecondary} />}
            {message.status === 'delivered' && <CheckCheck size={14} color={designTokens.colors.semantic.textSecondary} />}
            {message.status === 'read' && <CheckCheck size={14} color={designTokens.colors.semantic.accent} />}
          </View>
        )}
      </View>
    </View>
  );

  const renderMessageItem = ({ item }: { item: Message }) => {
    const isUser = item.sender === 'user';

    switch (item.type) {
      case 'image':
        return (
          <ImageMessage
            images={item.images || []}
            isUser={isUser}
            timestamp={item.timestamp}
            status={item.status}
          />
        );
      case 'voice':
        return (
          <VoiceMessage
            duration={item.voiceDuration || 0}
            isUser={isUser}
            timestamp={item.timestamp}
            status={item.status}
            audioUrl={item.audioUrl}
          />
        );
      case 'text':
      default:
        return <MessageBubble message={item} isUser={isUser} />;
    }
  };
  
  // Component: Chat Header with design tokens - Consistent with Home Page
  const ChatHeader: React.FC<ChatHeaderProps> = ({ companion, onBack, onCall, onVideoCall, onMore }) => (
    <View style={styles.header}>
      <View style={styles.headerContent}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={onBack}
          accessibilityRole="button"
          accessibilityLabel="Go back"
        >
          <ArrowLeft size={24} color={designTokens.colors.semantic.text} />
        </TouchableOpacity>

        <TouchableOpacity
          style={styles.profileButton}
          accessibilityRole="button"
          accessibilityLabel={`View ${companion.name} profile`}
        >
          <ProfileImage
            uri={companion.image}
            size="small"
            online={companion.online}
          />
          <View style={styles.profileInfo}>
            <Body style={styles.profileName}>{companion.name}</Body>
            {companion.online ? (
              <Caption style={styles.onlineStatus}>Online</Caption>
            ) : (
              <Caption style={styles.offlineStatus}>Last seen today</Caption>
            )}
          </View>
        </TouchableOpacity>

        <View style={styles.headerActions}>
          <TouchableOpacity
            style={styles.headerActionButton}
            onPress={onCall}
            accessibilityRole="button"
            accessibilityLabel="Call"
          >
            <Phone size={20} color={designTokens.colors.semantic.text} />
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.headerActionButton}
            onPress={onVideoCall}
            accessibilityRole="button"
            accessibilityLabel="Video call"
          >
            <Video size={20} color={designTokens.colors.semantic.text} />
          </TouchableOpacity>

          {/* <TouchableOpacity
            style={[
              styles.headerActionButton,
              showTranslation && styles.headerActionButtonActive,
            ]}
            onPress={() => setShowTranslation(!showTranslation)}
            accessibilityRole="button"
            accessibilityLabel="Toggle translation"
          >
            <Languages size={20} color={
              showTranslation
                ? designTokens.colors.semantic.primaryContrast
                : designTokens.colors.semantic.text
            } />
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.headerActionButton}
            onPress={() => setShowChatSettings(true)}
            accessibilityRole="button"
            accessibilityLabel="Chat settings"
          >
            <MoreVertical size={20} color={designTokens.colors.semantic.text} />
          </TouchableOpacity> */}
        </View>
      </View>
    </View>
  );

  return (
    <SafeAreaView style={styles.container} edges={['top', 'bottom']}>
      <KeyboardAvoidingView
        style={styles.keyboardContainer}
        behavior={Platform.OS === 'ios' ? 'padding' : undefined}
        keyboardVerticalOffset={Platform.OS === 'ios' ? 0 : 0}
      >
        <ChatHeader
          companion={companion}
          onBack={handleBack}
          onCall={() => console.log('Call:', companion.name)}
          onVideoCall={() => console.log('Video call:', companion.name)}
          onMore={() => console.log('More options')}
        />

        <View style={styles.chatContainer}>
          <FlatList
            ref={flatListRef}
            data={messages}
            renderItem={renderMessageItem}
            keyExtractor={item => item.id}
            contentContainerStyle={styles.messagesList}
          />

          <TypingIndicator companion={companion} visible={isTyping} />
        </View>

        {/* Translation Toggle */}
        {showTranslation && (
          <TranslationToggle
            isEnabled={translationEnabled}
            onToggle={handleTranslationToggle}
            onLanguageSelect={handleLanguageSelect}
          />
        )}

        <EnhancedMessageInput
          message={message}
          onMessageChange={setMessage}
          onSend={handleSend}
          onImageSelect={handleImageSelect}
          onVoiceRecord={handleVoiceRecord}
          onEmojiPress={handleEmojiPress}
          disabled={false}
          isRecording={isRecording}
          recordingDuration={recordingDuration}
        />

        {/* Chat Settings Modal */}
        <ChatSettings
          visible={showChatSettings}
          onClose={() => setShowChatSettings(false)}
          companion={companion}
          onViewProfile={() => {
            setShowChatSettings(false);
            // console.log('View profile:', companion.name);
          }}
          onMute={(muted) => console.log('Mute:', muted)}
          onBlock={() => console.log('Block:', companion.name)}
          onReport={() => console.log('Report:', companion.name)}
          onArchive={() => console.log('Archive chat')}
          onDelete={() => console.log('Delete chat')}
          onStarConversation={(starred) => console.log('Star:', starred)}
        />
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
}

// Styles using design tokens for consistency and maintainability
const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: designTokens.colors.semantic.background,
  },
  keyboardContainer: {
    flex: 1,
  },
  header: {
    paddingTop: designTokens.spacing.scale.lg,
    paddingBottom: designTokens.spacing.scale.md,
    paddingHorizontal: designTokens.spacing.scale.lg,
    backgroundColor: designTokens.colors.semantic.background,
    borderBottomWidth: 1,
    borderBottomColor: designTokens.colors.semantic.border,
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: designTokens.spacing.scale.sm,
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: designTokens.colors.semantic.surface,
    ...designTokens.shadows.sm,
  },
  profileButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    gap: designTokens.spacing.scale.sm,
  },
  profileInfo: {
    gap: 2,
  },
  profileName: {
    ...componentTokens.text.body,
    fontWeight: '600',
    color: designTokens.colors.semantic.text,
  },
  onlineStatus: {
    ...componentTokens.text.caption,
    color: designTokens.colors.semantic.accent,
  },
  offlineStatus: {
    ...componentTokens.text.caption,
    color: designTokens.colors.semantic.textSecondary,
  },
  headerActions: {
    flexDirection: 'row',
    gap: designTokens.spacing.scale.sm,
  },
  headerActionButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: designTokens.colors.semantic.surface,
    ...designTokens.shadows.sm,
  },
  headerActionButtonActive: {
    backgroundColor: designTokens.colors.semantic.primary,
  },
  chatContainer: {
    flex: 1,
    backgroundColor: designTokens.colors.semantic.background,
  },
  messagesList: {
    padding: designTokens.spacing.scale.md,
    paddingBottom: designTokens.spacing.scale.lg,
    gap: designTokens.spacing.scale.md,
  },
  messageContainer: {
    maxWidth: '80%',
    gap: designTokens.spacing.scale.xs,
  },
  userMessageContainer: {
    alignSelf: 'flex-end',
  },
  companionMessageContainer: {
    alignSelf: 'flex-start',
  },
  messageBubble: {
    borderRadius: designTokens.borderRadius.components.card,
    paddingHorizontal: designTokens.spacing.scale.md,
    paddingVertical: designTokens.spacing.scale.sm,
  },
  userMessageBubble: {
    backgroundColor: designTokens.colors.semantic.primary,
    borderBottomRightRadius: 4,
    ...designTokens.shadows.sm,
  },
  companionMessageBubble: {
    backgroundColor: designTokens.colors.semantic.surface,
    borderBottomLeftRadius: 4,
    ...designTokens.shadows.md,
  },
  messageText: {
    ...componentTokens.text.body,
  },
  userMessageText: {
    color: designTokens.colors.components.button.text,
  },
  companionMessageText: {
    color: designTokens.colors.semantic.text,
  },
  messageFooter: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: designTokens.spacing.scale.sm,
    gap: designTokens.spacing.scale.xs,
  },
  userMessageFooter: {
    justifyContent: 'flex-end',
  },
  companionMessageFooter: {
    justifyContent: 'flex-start',
  },
  messageTime: {
    ...componentTokens.text.caption,
    color: designTokens.colors.semantic.textSecondary,
  },
  messageStatus: {
    // No additional margin needed due to gap in parent
  },
  typingContainer: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    paddingHorizontal: designTokens.spacing.scale.md,
    marginBottom: designTokens.spacing.scale.md,
    gap: designTokens.spacing.scale.sm,
  },
  typingBubble: {
    backgroundColor: designTokens.colors.semantic.surface,
    borderRadius: designTokens.borderRadius.components.card,
    borderBottomLeftRadius: 4,
    paddingHorizontal: designTokens.spacing.scale.md,
    paddingVertical: designTokens.spacing.scale.sm,
    ...designTokens.shadows.md,
  },
  typingDots: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: designTokens.spacing.scale.xs,
  },
  typingDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: designTokens.colors.semantic.accent, // Coral accent for typing dots
  },
  typingDot1: {
    opacity: 0.4,
  },
  typingDot2: {
    opacity: 0.7,
  },
  typingDot3: {
    opacity: 1,
  },
  inputContainer: {
    backgroundColor: designTokens.colors.semantic.surface,
    borderTopWidth: 1,
    borderTopColor: designTokens.colors.semantic.border,
    padding: designTokens.spacing.scale.sm,
    gap: designTokens.spacing.scale.sm,
  },
  inputActions: {
    flexDirection: 'row',
    gap: designTokens.spacing.scale.xs,
  },
  inputActionButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  textInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: designTokens.colors.semantic.background,
    borderRadius: 24,
    paddingHorizontal: designTokens.spacing.scale.md,
    paddingVertical: designTokens.spacing.scale.sm,
    gap: designTokens.spacing.scale.sm,
    ...designTokens.shadows.sm,
  },
  textInput: {
    flex: 1,
    ...componentTokens.text.body,
    maxHeight: 100,
    color: designTokens.colors.semantic.text,
  },
  sendButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    ...designTokens.shadows.sm,
  },
  sendButtonDisabled: {
    backgroundColor: 'transparent',
  },
  sendButtonActive: {
    backgroundColor: designTokens.colors.semantic.accent, // Coral accent for send button
  },
});