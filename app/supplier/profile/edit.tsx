import React, { useState, useCallback, useEffect, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  TextInput,
  Image,
  Alert,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useRouter } from 'expo-router';
import {
  ArrowLeft,
  Save,
  Camera,
  Edit3,
  User,
  Mail,
  Phone,
  MapPin,
  Globe,
  Star,
  Award,
  Languages,
  Plus,
  X,
  Instagram,
  Facebook,
  Twitter,
  Clock,
  Users,
} from 'lucide-react-native';
import { RadialGradient } from '@/components/ui/RadialGradient';
import { Card } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { Heading, Subheading, Body, Caption } from '@/components/ui/Typography';
import { designTokens, componentTokens } from '@/constants/design-tokens';
import { useCompanionProfile, useCreateOrUpdateCompanionProfile, CompanionProfile, CompanionProfileRequest, CompanionProfileResponse } from '@/app/api/companion/profile';
import { useAuthStore } from '@/stores/auth-store';
import { useQueryClient } from '@tanstack/react-query';
import * as ExpoImagePicker from 'expo-image-picker';

interface SupplierProfile {
  id: string;
  firstName: string;
  lastName: string;
  displayName: string;
  bio: string;
  email: string;
  phone: string;
  location: string;
  profilePhoto: string;
  coverPhoto: string;
  gallery: string[];
  languages: string[];
  specialization: string[];
  certifications: string[];
  socialLinks: {
    website?: string;
    instagram?: string;
    facebook?: string;
    twitter?: string;
  };
  experienceStats: {
    yearsOfExperience: number;
    totalGuests: number;
    totalTours: number;
    averageRating: number;
    responseTime: string;
  };
  availability: {
    workingDays: string[];
    workingHours: { start: string; end: string };
    timeZone: string;
  };
}

// Helper to map API response to camelCase state
function mapProfileFromApi(data: any) {
  return {
    id: data.id,
    userId: data.user_id,
    firstName: data.first_name,
    lastName: data.last_name,
    displayName: data.display_name,
    bio: data.bio,
    email: data.email,
    phone: data.phone,
    location: data.location,
    profilePhoto: data.profile_photo,
    coverPhoto: data.cover_photo,
    gallery: data.gallery,
    languages: data.languages,
    specialization: data.specialization,
    certifications: data.certifications,
    socialLinks: typeof data.social_links === 'string'
      ? JSON.parse(data.social_links)
      : (data.social_links || {}),
    experienceStats: data.experienceStats,
    dateOfBirth: data.date_of_birth,
    gender: data.gender,
    createdAt: data.created_at,
    updatedAt: data.updated_at,
  };
}

export default function ProfileEditScreen() {
  const router = useRouter();
  const { user } = useAuthStore();
  const queryClient = useQueryClient();
  // API integration
  const { data: profileData, isLoading: profileLoading } = useCompanionProfile();
  const createOrUpdateProfile = useCreateOrUpdateCompanionProfile();

  // Local state for editing
  const [profile, setProfile] = useState<CompanionProfile | null>(null);
  const [coverPhotoUri, setCoverPhotoUri] = useState<string | null>(null);
  const [profilePhotoUri, setProfilePhotoUri] = useState<string | null>(null);

  // Populate form when API data loads
  useEffect(() => {
    if (profileData?.data) {
      setProfile(mapProfileFromApi(profileData.data));
    }
  }, [profileData]);

  // Helper for safe field update
  const updateProfile = (field: keyof CompanionProfile, value: any) => {
    setProfile(prev => {
      if (!prev) return prev;
      return { ...prev, [field]: value } as CompanionProfile;
    });
  };

  // Helper for nested field update
  const updateNestedField = (parent: keyof CompanionProfile, field: string, value: any) => {
    setProfile(prev => {
      if (!prev) return prev;
      return {
      ...prev,
      [parent]: {
        ...(prev[parent] as any),
        [field]: value,
      },
      } as CompanionProfile;
    });
  };

  const addListItem = (listType: 'languages' | 'specialization' | 'certifications', value: string) => {
    if (value.trim()) {
      setProfile(prev => {
        if (!prev) return prev;
        return {
        ...prev,
          [listType]: [...(prev[listType] as string[]), value.trim()]
        } as CompanionProfile;
      });
    }
  };

  const removeListItem = (listType: 'languages' | 'specialization' | 'certifications', index: number) => {
    setProfile(prev => {
      if (!prev) return prev;
      return {
      ...prev,
        [listType]: (prev[listType] as string[]).filter((_: string, i: number) => i !== index)
      } as CompanionProfile;
    });
  };
  
  // Image picker logic
  const pickImage = async (onPick: (uri: string) => void) => {
    const result = await ExpoImagePicker.launchImageLibraryAsync({
      mediaTypes: ExpoImagePicker.MediaTypeOptions.Images,
      allowsEditing: true,
      aspect: [1, 1],
      quality: 0.8,
    });
    if (!result.canceled && result.assets && result.assets.length > 0) {
      onPick(result.assets[0].uri);
    }
  };

  const handleSave = async () => {
    if (!profile) return;
    if (!profile.firstName.trim() || !profile.lastName.trim() || !profile.displayName.trim()) {
      Alert.alert('Error', 'Please fill in all required fields');
      return;
    }
    // Prepare payload for API
    const payload: any = {
      firstName: profile.firstName,
      lastName: profile.lastName,
      displayName: profile.displayName,
      bio: profile.bio,
      socialLinks: profile.socialLinks && typeof profile.socialLinks === 'object' ? profile.socialLinks : {},
      gender: profile.gender as 'male' | 'female' | 'other',
      location: profile.location,
      languages: profile.languages,
      specialization: profile.specialization,
      certifications: profile.certifications,
    };
    if (profile.dateOfBirth) payload.dateOfBirth = profile.dateOfBirth;
    if (coverPhotoUri) payload.coverPhoto = coverPhotoUri;
    if (profilePhotoUri) payload.profilePhoto = profilePhotoUri;
    console.log('payload:', payload);
    try {
      if (coverPhotoUri || profilePhotoUri) {
        // Use multipart/form-data
        const formData = new FormData();
        formData.append('data', JSON.stringify(payload));
        if (coverPhotoUri) {
          formData.append('coverPhoto', {
            uri: coverPhotoUri,
            name: 'cover.jpg',
            type: 'image/jpeg',
          } as any);
        }
        if (profilePhotoUri) {
          formData.append('profilePhoto', {
            uri: profilePhotoUri,
            name: 'profile.jpg',
            type: 'image/jpeg',
          } as any);
        }
        await createOrUpdateProfile.mutateAsync(formData);
        await queryClient.invalidateQueries({ queryKey: ['companionProfile'] });
        const newProfileData = await queryClient.fetchQuery({ queryKey: ['companionProfile'] }) as CompanionProfileResponse;
        setProfile(mapProfileFromApi(newProfileData.data));
        setCoverPhotoUri(null);
        setProfilePhotoUri(null);
      } else {
        await createOrUpdateProfile.mutateAsync(payload);
        await queryClient.invalidateQueries({ queryKey: ['companionProfile'] });
        const newProfileData = await queryClient.fetchQuery({ queryKey: ['companionProfile'] }) as CompanionProfileResponse;
        setProfile(mapProfileFromApi(newProfileData.data));
      }
      Alert.alert('Profile Updated', 'Your profile has been updated successfully.', [
        { text: 'OK', onPress: () => router.back() },
      ]);
    } catch (e) {
      Alert.alert('Error', e instanceof Error ? e.message : 'Failed to update profile');
    }
  };

  const ListEditor = ({
    title,
    items,
    onAdd,
    onRemove,
    placeholder
  }: {
    title: string;
    items: string[];
    onAdd: (value: string) => void;
    onRemove: (index: number) => void;
    placeholder: string;
  }) => {
    const [inputValue, setInputValue] = useState('');
    const inputRef = useRef<TextInput>(null);

    const handleAdd = () => {
      if (!inputValue.trim()) return;
      onAdd(inputValue.trim());
      setInputValue('');
      setTimeout(() => {
        inputRef.current?.focus();
      }, 100);
    };

    return (
    <View style={styles.listEditor}>
      <Body style={styles.listTitle}>{title}</Body>
        <View style={styles.addItemContainer}>
          <TextInput
            ref={inputRef}
            style={styles.addItemInput}
            placeholder={placeholder}
            value={inputValue}
            onChangeText={setInputValue}
            onSubmitEditing={handleAdd}
            blurOnSubmit={false}
          />
          <TouchableOpacity
            style={styles.addButton}
            onPress={handleAdd}
          >
            <Plus size={16} color={designTokens.colors.semantic.primary} />
          </TouchableOpacity>
        </View>
      {items.map((item, index) => (
        <View key={index} style={styles.listItem}>
          <Text style={styles.listItemText}>{item}</Text>
          <TouchableOpacity
            style={styles.removeButton}
            onPress={() => onRemove(index)}
          >
            <X size={16} color={designTokens.colors.semantic.error} />
          </TouchableOpacity>
        </View>
      ))}
    </View>
  );
  };


  if (!profile) {
    return (
      <SafeAreaView style={styles.loadingContainer}>
        <Text style={styles.loadingText}>Loading profile...</Text>
      </SafeAreaView>
    );
  }
  // Debug: log image URIs before rendering
  // console.log('coverPhoto:', coverPhotoUri, profile?.coverPhoto);
  // console.log('profilePhoto:', profilePhotoUri, profile?.profilePhoto);
  return (
    <RadialGradient variant="appBackground" style={styles.container}>
      {/* Header */}
      <SafeAreaView edges={['top']} style={styles.header}>
        <View style={styles.headerContent}>
          <TouchableOpacity style={styles.backButton} onPress={() => router.back()}>
            <ArrowLeft size={24} color={designTokens.colors.semantic.text} />
          </TouchableOpacity>

          <View style={styles.headerTitle}>
            <Subheading style={styles.title}>Edit Profile</Subheading>
            <Caption style={styles.subtitle}>Update your professional information</Caption>
          </View>

          <TouchableOpacity style={styles.saveButton} onPress={handleSave}>
            <Save size={20} color={designTokens.colors.semantic.surface} />
          </TouchableOpacity>
        </View>
      </SafeAreaView>

      {/* Content */}
      <ScrollView
        style={styles.content}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.scrollContent}
      >
        {/* Profile Photos */}
        <Card style={styles.section} padding={16}>
          <Subheading style={styles.sectionTitle}>Profile Photos</Subheading>

          {/* Cover Photo */}
          <View style={styles.coverPhotoContainer}>
            <Image
              source={{
                uri: (coverPhotoUri || profile?.coverPhoto)
                  ? (coverPhotoUri || profile?.coverPhoto) + '?t=' + Date.now()
                  : undefined,
              }}
              style={styles.coverPhoto}
            />
            <TouchableOpacity style={styles.editCoverButton} onPress={() => pickImage(setCoverPhotoUri)}>
              <Edit3 size={16} color={designTokens.colors.semantic.surface} />
            </TouchableOpacity>
            <Caption style={styles.photoLabel}>Cover Photo</Caption>
          </View>

          {/* Profile Photo */}
          <View style={styles.profilePhotoContainer}>
            <Image
              source={{
                uri: (profilePhotoUri || profile?.profilePhoto)
                  ? (profilePhotoUri || profile?.profilePhoto) + '?t=' + Date.now()
                  : undefined,
              }}
              style={styles.profilePhoto}
            />
            <TouchableOpacity style={styles.editProfileButton} onPress={() => pickImage(setProfilePhotoUri)}>
              <Edit3 size={12} color={designTokens.colors.semantic.surface} />
            </TouchableOpacity>
            <Caption style={styles.photoLabel}>Profile Photo</Caption>
          </View>
        </Card>
        {/* Basic Information */}
        <Card style={styles.section} padding={16}>
          <Subheading style={styles.sectionTitle}>Basic Information</Subheading>

          <View style={styles.row}>
            <View style={styles.halfInput}>
              <Body style={styles.inputLabel}>First Name *</Body>
              <TextInput
                style={styles.textInput}
                value={profile?.firstName}
                onChangeText={(text) => updateProfile('firstName', text)}
                placeholder="Enter first name"
              />
            </View>

            <View style={styles.halfInput}>
              <Body style={styles.inputLabel}>Last Name *</Body>
              <TextInput
                style={styles.textInput}
                value={profile?.lastName}
                onChangeText={(text) => updateProfile('lastName', text)}
                placeholder="Enter last name"
              />
            </View>
          </View>

          <View style={styles.inputGroup}>
            <Body style={styles.inputLabel}>Display Name</Body>
            <TextInput
              style={styles.textInput}
              value={profile?.displayName}
              onChangeText={(text) => updateProfile('displayName', text)}
              placeholder="How you want to be known to customers"
            />
          </View>

          <View style={styles.inputGroup}>
            <Body style={styles.inputLabel}>Bio *</Body>
            <TextInput
              style={[styles.textInput, styles.textArea]}
              value={profile?.bio}
              onChangeText={(text) => updateProfile('bio', text)}
              placeholder="Tell customers about yourself, your experience, and what makes you special..."
              multiline
              numberOfLines={4}
              textAlignVertical="top"
              maxLength={500}
            />
            <Caption style={styles.characterCount}>
              {profile?.bio?.length}/500 characters
            </Caption>
          </View>
        </Card>

        {/* Contact Information */}
        <Card style={styles.section} padding={16}>
          <Subheading style={styles.sectionTitle}>Contact Information</Subheading>

          <View style={styles.inputGroup}>
            <Body style={styles.inputLabel}>Location</Body>
            <View style={styles.inputWithIcon}>
              <MapPin size={16} color={designTokens.colors.semantic.primary} />
              <TextInput
                style={styles.inputWithIconText}
                value={profile?.location}
                onChangeText={(text) => updateProfile('location', text)}
                placeholder="City, Country"
              />
            </View>
          </View>

          <View style={styles.inputGroup}>
            <Body style={styles.inputLabel}>Email Address</Body>
            <View style={styles.inputWithIcon}>
              <Mail size={16} color={designTokens.colors.semantic.primary} />
              <TextInput
                style={styles.inputWithIconText}
                value={user?.email || ''}
                placeholder="<EMAIL>"
                keyboardType="email-address"
                autoCapitalize="none"
                editable={true}
              />
            </View>
          </View>

          <View style={styles.inputGroup}>
            <Body style={styles.inputLabel}>Phone Number</Body>
            <View style={styles.inputWithIcon}>
              <Phone size={16} color={designTokens.colors.semantic.primary} />
              <TextInput
                style={styles.inputWithIconText}
                value={user?.phone || ''}
                placeholder="+66 XX XXX XXXX"
                keyboardType="phone-pad"
                editable={true}
              />
            </View>
          </View>
        </Card>

        {/* Professional Information */}
        <Card style={styles.section} padding={16}>
          <Subheading style={styles.sectionTitle}>Professional Information</Subheading>

          <ListEditor
            title="Languages"
            items={profile?.languages || []}
            onAdd={val => addListItem('languages', val)}
            onRemove={index => removeListItem('languages', index)}
            placeholder="Add language (e.g., English - Fluent)"
          />

          <ListEditor
            title="Specializations"
            items={profile?.specialization || []}
            onAdd={val => addListItem('specialization', val)}
            onRemove={index => removeListItem('specialization', index)}
            placeholder="Add specialization (e.g., Street Food Tours)"
          />

          <ListEditor
            title="Certifications"
            items={profile?.certifications || []}
            onAdd={val => addListItem('certifications', val)}
            onRemove={index => removeListItem('certifications', index)}
            placeholder="Add certification (e.g., Licensed Tour Guide)"
          />
        </Card>

        {/* Social Links */}
        <Card style={styles.section} padding={16}>
          <Subheading style={styles.sectionTitle}>Social Links</Subheading>

          <View style={styles.inputGroup}>
            <Body style={styles.inputLabel}>Website</Body>
            <View style={styles.inputWithIcon}>
              <Globe size={16} color={designTokens.colors.semantic.primary} />
              <TextInput
                style={styles.inputWithIconText}
                value={profile?.socialLinks?.website || ''}
                onChangeText={(text) => updateNestedField('socialLinks', 'website', text)}
                placeholder="https://your-website.com"
                autoCapitalize="none"
              />
            </View>
          </View>

          <View style={styles.inputGroup}>
            <Body style={styles.inputLabel}>Instagram</Body>
            <View style={styles.inputWithIcon}>
              <Instagram size={16} color={designTokens.colors.semantic.primary} />
              <TextInput
                style={styles.inputWithIconText}
                value={profile?.socialLinks?.instagram || ''}
                onChangeText={(text) => updateNestedField('socialLinks', 'instagram', text)}
                placeholder="@your_instagram"
                autoCapitalize="none"
              />
            </View>
          </View>

          <View style={styles.inputGroup}>
            <Body style={styles.inputLabel}>Facebook</Body>
            <View style={styles.inputWithIcon}>
              <Facebook size={16} color={designTokens.colors.semantic.primary} />
              <TextInput
                style={styles.inputWithIconText}
                value={profile?.socialLinks?.facebook || ''}
                onChangeText={(text) => updateNestedField('socialLinks', 'facebook', text)}
                placeholder="Your Facebook Page"
                autoCapitalize="none"
              />
            </View>
          </View>
        </Card>

        {/* Experience Stats */}
        <Card style={styles.section} padding={16}>
          <Subheading style={styles.sectionTitle}>Experience Statistics</Subheading>
          <Caption style={styles.sectionDescription}>
            These stats are automatically calculated based on your activity
          </Caption>

          <View style={styles.statsGrid}>
            <View style={styles.statItem}>
              <Award size={16} color={designTokens.colors.semantic.primary} />
              <View style={styles.statContent}>
                <Body style={styles.statValue}>{profile?.experienceStats.yearsOfExperience}</Body>
                <Caption style={styles.statLabel}>Years Experience</Caption>
              </View>
            </View>

            <View style={styles.statItem}>
              <Users size={16} color={designTokens.colors.semantic.success} />
              <View style={styles.statContent}>
                <Body style={styles.statValue}>{profile?.experienceStats.totalGuests.toLocaleString()}</Body>
                <Caption style={styles.statLabel}>Total Guests</Caption>
              </View>
            </View>

            <View style={styles.statItem}>
              <Star size={16} color="#FFD700" />
              <View style={styles.statContent}>
                <Body style={styles.statValue}>{profile?.experienceStats.averageRating}</Body>
                <Caption style={styles.statLabel}>Average Rating</Caption>
              </View>
            </View>

            <View style={styles.statItem}>
              <Clock size={16} color={designTokens.colors.semantic.accent} />
              <View style={styles.statContent}>
                <Body style={styles.statValue}>{profile?.experienceStats.responseTime}</Body>
                <Caption style={styles.statLabel}>Response Time</Caption>
              </View>
            </View>
          </View>
        </Card>
      </ScrollView>
    </RadialGradient>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    backgroundColor: 'transparent',
    paddingHorizontal: designTokens.spacing.scale.md,
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: designTokens.spacing.scale.md,
  },
  backButton: {
    width: 44,
    height: 44,
    borderRadius: 22,
    backgroundColor: designTokens.colors.semantic.surface,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: designTokens.spacing.scale.md,
  },
  headerTitle: {
    flex: 1,
  },
  title: {
    marginBottom: designTokens.spacing.scale.xs,
  },
  subtitle: {
    color: designTokens.colors.semantic.textSecondary,
  },
  saveButton: {
    width: 44,
    height: 44,
    borderRadius: 22,
    backgroundColor: designTokens.colors.semantic.primary,
    justifyContent: 'center',
    alignItems: 'center',
  },
  content: {
    flex: 1,
  },
  scrollContent: {
    padding: designTokens.spacing.scale.md,
    paddingBottom: designTokens.spacing.scale.xl,
  },
  section: {
    marginBottom: designTokens.spacing.scale.md,
  },
  sectionTitle: {
    marginBottom: designTokens.spacing.scale.md,
  },
  sectionDescription: {
    color: designTokens.colors.semantic.textSecondary,
    marginBottom: designTokens.spacing.scale.md,
  },
  coverPhotoContainer: {
    marginBottom: designTokens.spacing.scale.md,
    position: 'relative',
  },
  coverPhoto: {
    width: '100%',
    height: 120,
    borderRadius: designTokens.borderRadius.components.card,
  },
  editCoverButton: {
    position: 'absolute',
    top: 8,
    right: 8,
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: designTokens.colors.semantic.primary,
    justifyContent: 'center',
    alignItems: 'center',
  },
  profilePhotoContainer: {
    alignItems: 'center',
    marginBottom: designTokens.spacing.scale.md,
    position: 'relative',
  },
  profilePhoto: {
    width: 100,
    height: 100,
    borderRadius: 50,
    borderWidth: 4,
    borderColor: designTokens.colors.semantic.surface,
  },
  editProfileButton: {
    position: 'absolute',
    bottom: 20,
    right: '35%',
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: designTokens.colors.semantic.primary,
    justifyContent: 'center',
    alignItems: 'center',
  },
  photoLabel: {
    marginTop: designTokens.spacing.scale.xs,
    textAlign: 'center',
    color: designTokens.colors.semantic.textSecondary,
  },
  inputGroup: {
    marginBottom: designTokens.spacing.scale.md,
  },
  inputLabel: {
    fontWeight: '500',
    marginBottom: designTokens.spacing.scale.sm,
  },
  textInput: {
    backgroundColor: designTokens.colors.semantic.surface,
    borderRadius: designTokens.borderRadius.components.card,
    padding: designTokens.spacing.scale.md,
    fontSize: 16,
    color: designTokens.colors.semantic.text,
    borderWidth: 1,
    borderColor: designTokens.colors.semantic.border,
  },
  textArea: {
    minHeight: 100,
  },
  characterCount: {
    textAlign: 'right',
    color: designTokens.colors.semantic.textSecondary,
    marginTop: designTokens.spacing.scale.xs,
  },
  row: {
    flexDirection: 'row',
    gap: designTokens.spacing.scale.md,
    marginBottom: designTokens.spacing.scale.md,
  },
  halfInput: {
    flex: 1,
  },
  inputWithIcon: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: designTokens.colors.semantic.surface,
    borderRadius: designTokens.borderRadius.components.card,
    padding: designTokens.spacing.scale.md,
    borderWidth: 1,
    borderColor: designTokens.colors.semantic.border,
  },
  inputWithIconText: {
    flex: 1,
    marginLeft: designTokens.spacing.scale.sm,
    fontSize: 16,
    color: designTokens.colors.semantic.text,
  },
  listEditor: {
    marginBottom: designTokens.spacing.scale.lg,
  },
  listTitle: {
    fontWeight: '500',
    marginBottom: designTokens.spacing.scale.sm,
  },
  listItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: designTokens.colors.semantic.surface + '80',
    padding: designTokens.spacing.scale.sm,
    borderRadius: designTokens.borderRadius.components.card,
    marginBottom: designTokens.spacing.scale.xs,
  },
  listItemText: {
    flex: 1,
    color: designTokens.colors.semantic.text,
  },
  removeButton: {
    padding: designTokens.spacing.scale.xs,
  },
  addItemContainer: {
    flexDirection: 'row',
    gap: designTokens.spacing.scale.sm,
  },
  addItemInput: {
    flex: 1,
    backgroundColor: designTokens.colors.semantic.surface,
    borderRadius: designTokens.borderRadius.components.card,
    padding: designTokens.spacing.scale.sm,
    fontSize: 14,
    color: designTokens.colors.semantic.text,
    borderWidth: 1,
    borderColor: designTokens.colors.semantic.border,
  },
  addButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: designTokens.colors.semantic.primary + '20',
    justifyContent: 'center',
    alignItems: 'center',
  },
  statsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: designTokens.spacing.scale.md,
  },
  statItem: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: designTokens.colors.semantic.surface + '80',
    padding: designTokens.spacing.scale.sm,
    borderRadius: designTokens.borderRadius.components.card,
    flex: 1,
    minWidth: '45%',
  },
  statContent: {
    marginLeft: designTokens.spacing.scale.sm,
  },
  statValue: {
    fontWeight: '600',
    marginBottom: 2,
  },
  statLabel: {
    color: designTokens.colors.semantic.textSecondary,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    color: designTokens.colors.semantic.text,
  },
});