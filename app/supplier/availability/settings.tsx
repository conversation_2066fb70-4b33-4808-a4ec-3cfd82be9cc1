import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Switch,
  Alert,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useRouter } from 'expo-router';
import {
  ArrowLeft,
  Clock,
  Calendar,
  Bell,
  Globe,
  Users,
  Settings as SettingsIcon,
  Save,
  RefreshCw,
  AlertTriangle,
  CheckCircle,
} from 'lucide-react-native';
import { RadialGradient } from '@/components/ui/RadialGradient';
import { Card } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { Heading, Subheading, Body, Caption } from '@/components/ui/Typography';
import { designTokens, componentTokens } from '@/constants/design-tokens';

interface AvailabilitySettings {
  workingDays: string[];
  workingHours: {
    start: string;
    end: string;
  };
  timeZone: string;
  bufferTime: number; // minutes between bookings
  maxAdvanceBooking: number; // days
  minAdvanceBooking: number; // hours
  autoAcceptBookings: boolean;
  instantBooking: boolean;
  notifications: {
    newBooking: boolean;
    bookingReminder: boolean;
    cancellation: boolean;
    rescheduling: boolean;
  };
  pricing: {
    weekendPremium: number; // percentage
    holidayPremium: number; // percentage
    lastMinuteDiscount: number; // percentage
  };
}

export default function AvailabilitySettingsScreen() {
  const router = useRouter();

  const [settings, setSettings] = useState<AvailabilitySettings>({
    workingDays: ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday'],
    workingHours: {
      start: '08:00',
      end: '18:00',
    },
    timeZone: 'Asia/Bangkok',
    bufferTime: 30,
    maxAdvanceBooking: 90,
    minAdvanceBooking: 24,
    autoAcceptBookings: false,
    instantBooking: true,
    notifications: {
      newBooking: true,
      bookingReminder: true,
      cancellation: true,
      rescheduling: true,
    },
    pricing: {
      weekendPremium: 20,
      holidayPremium: 30,
      lastMinuteDiscount: 10,
    },
  });

  const updateSettings = (field: keyof AvailabilitySettings, value: any) => {
    setSettings(prev => ({ ...prev, [field]: value }));
  };

  const updateNestedSettings = (parent: keyof AvailabilitySettings, field: string, value: any) => {
    setSettings(prev => ({
      ...prev,
      [parent]: {
        ...(prev[parent] as any),
        [field]: value,
      },
    }));
  };

  const toggleWorkingDay = (day: string) => {
    const isSelected = settings.workingDays.includes(day);
    if (isSelected) {
      updateSettings('workingDays', settings.workingDays.filter(d => d !== day));
    } else {
      updateSettings('workingDays', [...settings.workingDays, day]);
    }
  };

  const handleSave = () => {
    // In a real app, this would save to API
    // console.log('Saving availability settings:', settings);
    
    Alert.alert(
      'Settings Saved',
      'Your availability settings have been updated successfully.',
      [{ text: 'OK', onPress: () => router.back() }]
    );
  };

  const handleReset = () => {
    Alert.alert(
      'Reset Settings',
      'Are you sure you want to reset all settings to default values?',
      [
        { text: 'Cancel', style: 'cancel' },
        { text: 'Reset', style: 'destructive', onPress: () => {
          // Reset to default values
          // console.log('Resetting settings to defaults');
        }},
      ]
    );
  };

  const weekDays = [
    { key: 'monday', label: 'Monday', short: 'Mon' },
    { key: 'tuesday', label: 'Tuesday', short: 'Tue' },
    { key: 'wednesday', label: 'Wednesday', short: 'Wed' },
    { key: 'thursday', label: 'Thursday', short: 'Thu' },
    { key: 'friday', label: 'Friday', short: 'Fri' },
    { key: 'saturday', label: 'Saturday', short: 'Sat' },
    { key: 'sunday', label: 'Sunday', short: 'Sun' },
  ];

  const timeZones = [
    { value: 'Asia/Bangkok', label: 'Bangkok (UTC+7)' },
    { value: 'Asia/Singapore', label: 'Singapore (UTC+8)' },
    { value: 'Asia/Tokyo', label: 'Tokyo (UTC+9)' },
    { value: 'Asia/Hong_Kong', label: 'Hong Kong (UTC+8)' },
  ];

  const SettingRow = ({ 
    icon, 
    title, 
    description, 
    value, 
    onPress,
    rightElement 
  }: {
    icon: React.ReactNode;
    title: string;
    description?: string;
    value?: string;
    onPress?: () => void;
    rightElement?: React.ReactNode;
  }) => (
    <TouchableOpacity style={styles.settingRow} onPress={onPress} disabled={!onPress}>
      <View style={styles.settingIcon}>
        {icon}
      </View>
      <View style={styles.settingContent}>
        <Body style={styles.settingTitle}>{title}</Body>
        {description && (
          <Caption style={styles.settingDescription}>{description}</Caption>
        )}
        {value && (
          <Caption style={styles.settingValue}>{value}</Caption>
        )}
      </View>
      {rightElement && (
        <View style={styles.settingRight}>
          {rightElement}
        </View>
      )}
    </TouchableOpacity>
  );

  const SwitchRow = ({ 
    icon, 
    title, 
    description, 
    value, 
    onValueChange 
  }: {
    icon: React.ReactNode;
    title: string;
    description?: string;
    value: boolean;
    onValueChange: (value: boolean) => void;
  }) => (
    <SettingRow
      icon={icon}
      title={title}
      description={description}
      rightElement={
        <Switch
          value={value}
          onValueChange={onValueChange}
          trackColor={{
            false: designTokens.colors.semantic.textSecondary + '40',
            true: designTokens.colors.semantic.primary + '40',
          }}
          thumbColor={value ? designTokens.colors.semantic.primary : designTokens.colors.semantic.textSecondary}
        />
      }
    />
  );

  return (
    <RadialGradient variant="appBackground" style={styles.container}>
      {/* Header */}
      <SafeAreaView edges={['top']} style={styles.header}>
        <View style={styles.headerContent}>
          <TouchableOpacity
            style={styles.backButton}
            onPress={() => router.back()}
          >
            <ArrowLeft size={24} color={designTokens.colors.semantic.text} />
          </TouchableOpacity>
          
          <View style={styles.headerTitle}>
            <Heading style={styles.title}>Availability Settings</Heading>
            <Caption style={styles.subtitle}>Configure your schedule and preferences</Caption>
          </View>

          <TouchableOpacity style={styles.saveButton} onPress={handleSave}>
            <Save size={20} color={designTokens.colors.semantic.surface} />
          </TouchableOpacity>
        </View>
      </SafeAreaView>

      <ScrollView
        style={styles.content}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.scrollContent}
      >
        {/* Working Days */}
        <Card style={styles.section} padding={16}>
          <Subheading style={styles.sectionTitle}>Working Days</Subheading>
          <Caption style={styles.sectionDescription}>
            Select the days you're available for bookings
          </Caption>
          
          <View style={styles.workingDaysGrid}>
            {weekDays.map((day) => (
              <TouchableOpacity
                key={day.key}
                style={[
                  styles.dayButton,
                  settings.workingDays.includes(day.key) && styles.dayButtonActive
                ]}
                onPress={() => toggleWorkingDay(day.key)}
              >
                <Text style={[
                  styles.dayButtonText,
                  settings.workingDays.includes(day.key) && styles.dayButtonTextActive
                ]}>
                  {day.short}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        </Card>

        {/* Working Hours */}
        <Card style={styles.section} padding={16}>
          <Subheading style={styles.sectionTitle}>Working Hours</Subheading>
          <Caption style={styles.sectionDescription}>
            Set your default working hours for selected days
          </Caption>
          
          <View style={styles.timeRow}>
            <View style={styles.timeInput}>
              <Caption style={styles.timeLabel}>Start Time</Caption>
              <TouchableOpacity style={styles.timeButton}>
                <Clock size={16} color={designTokens.colors.semantic.primary} />
                <Body style={styles.timeText}>{settings.workingHours.start}</Body>
              </TouchableOpacity>
            </View>
            
            <View style={styles.timeInput}>
              <Caption style={styles.timeLabel}>End Time</Caption>
              <TouchableOpacity style={styles.timeButton}>
                <Clock size={16} color={designTokens.colors.semantic.primary} />
                <Body style={styles.timeText}>{settings.workingHours.end}</Body>
              </TouchableOpacity>
            </View>
          </View>
        </Card>

        {/* Booking Rules */}
        <Card style={styles.section} padding={16}>
          <Subheading style={styles.sectionTitle}>Booking Rules</Subheading>
          
          <SettingRow
            icon={<Clock size={20} color={designTokens.colors.semantic.primary} />}
            title="Buffer Time"
            description="Time between bookings for preparation"
            value={`${settings.bufferTime} minutes`}
          />
          
          <SettingRow
            icon={<Calendar size={20} color={designTokens.colors.semantic.primary} />}
            title="Maximum Advance Booking"
            description="How far in advance customers can book"
            value={`${settings.maxAdvanceBooking} days`}
          />
          
          <SettingRow
            icon={<AlertTriangle size={20} color={designTokens.colors.semantic.warning} />}
            title="Minimum Advance Booking"
            description="Minimum notice required for bookings"
            value={`${settings.minAdvanceBooking} hours`}
          />
          
          <SwitchRow
            icon={<CheckCircle size={20} color={designTokens.colors.semantic.success} />}
            title="Auto-Accept Bookings"
            description="Automatically accept booking requests"
            value={settings.autoAcceptBookings}
            onValueChange={(value) => updateSettings('autoAcceptBookings', value)}
          />
          
          <SwitchRow
            icon={<RefreshCw size={20} color={designTokens.colors.semantic.accent} />}
            title="Instant Booking"
            description="Allow customers to book instantly without approval"
            value={settings.instantBooking}
            onValueChange={(value) => updateSettings('instantBooking', value)}
          />
        </Card>

        {/* Notifications */}
        <Card style={styles.section} padding={16}>
          <Subheading style={styles.sectionTitle}>Notifications</Subheading>
          <Caption style={styles.sectionDescription}>
            Choose which notifications you want to receive
          </Caption>
          
          <SwitchRow
            icon={<Bell size={20} color={designTokens.colors.semantic.primary} />}
            title="New Booking"
            description="Get notified when you receive a new booking"
            value={settings.notifications.newBooking}
            onValueChange={(value) => updateNestedSettings('notifications', 'newBooking', value)}
          />
          
          <SwitchRow
            icon={<Clock size={20} color={designTokens.colors.semantic.warning} />}
            title="Booking Reminder"
            description="Reminders before upcoming bookings"
            value={settings.notifications.bookingReminder}
            onValueChange={(value) => updateNestedSettings('notifications', 'bookingReminder', value)}
          />
          
          <SwitchRow
            icon={<AlertTriangle size={20} color={designTokens.colors.semantic.error} />}
            title="Cancellation"
            description="Get notified when bookings are cancelled"
            value={settings.notifications.cancellation}
            onValueChange={(value) => updateNestedSettings('notifications', 'cancellation', value)}
          />
          
          <SwitchRow
            icon={<RefreshCw size={20} color={designTokens.colors.semantic.accent} />}
            title="Rescheduling"
            description="Get notified when bookings are rescheduled"
            value={settings.notifications.rescheduling}
            onValueChange={(value) => updateNestedSettings('notifications', 'rescheduling', value)}
          />
        </Card>

        {/* Advanced Settings */}
        <Card style={styles.section} padding={16}>
          <Subheading style={styles.sectionTitle}>Advanced Settings</Subheading>
          
          <SettingRow
            icon={<Globe size={20} color={designTokens.colors.semantic.primary} />}
            title="Time Zone"
            description="Your local time zone for scheduling"
            value={timeZones.find(tz => tz.value === settings.timeZone)?.label}
          />
          
          <SettingRow
            icon={<Users size={20} color={designTokens.colors.semantic.accent} />}
            title="Weekend Premium"
            description="Extra charge for weekend bookings"
            value={`+${settings.pricing.weekendPremium}%`}
          />
          
          <SettingRow
            icon={<Calendar size={20} color={designTokens.colors.semantic.success} />}
            title="Holiday Premium"
            description="Extra charge for holiday bookings"
            value={`+${settings.pricing.holidayPremium}%`}
          />
        </Card>

        {/* Actions */}
        <View style={styles.actions}>
          <Button
            title="Reset to Defaults"
            onPress={handleReset}
            variant="outline"
            style={styles.resetButton}
            // icon={<RefreshCw size={16} color={designTokens.colors.semantic.primary} />}
          />
        </View>
      </ScrollView>
    </RadialGradient>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    backgroundColor: 'transparent',
    paddingHorizontal: designTokens.spacing.scale.md,
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: designTokens.spacing.scale.md,
  },
  backButton: {
    width: 44,
    height: 44,
    borderRadius: 22,
    backgroundColor: designTokens.colors.semantic.surface,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: designTokens.spacing.scale.md,
  },
  headerTitle: {
    flex: 1,
  },
  title: {
    marginBottom: designTokens.spacing.scale.xs,
  },
  subtitle: {
    color: designTokens.colors.semantic.textSecondary,
  },
  saveButton: {
    width: 44,
    height: 44,
    borderRadius: 22,
    backgroundColor: designTokens.colors.semantic.primary,
    justifyContent: 'center',
    alignItems: 'center',
  },
  content: {
    flex: 1,
  },
  scrollContent: {
    padding: designTokens.spacing.scale.md,
    paddingBottom: designTokens.spacing.scale.xl,
  },
  section: {
    marginBottom: designTokens.spacing.scale.md,
  },
  sectionTitle: {
    marginBottom: designTokens.spacing.scale.sm,
  },
  sectionDescription: {
    color: designTokens.colors.semantic.textSecondary,
    marginBottom: designTokens.spacing.scale.md,
  },
  workingDaysGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: designTokens.spacing.scale.sm,
  },
  dayButton: {
    width: 44,
    height: 44,
    borderRadius: 22,
    backgroundColor: designTokens.colors.semantic.surface + '80',
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: 'transparent',
  },
  dayButtonActive: {
    backgroundColor: designTokens.colors.semantic.primary,
    borderColor: designTokens.colors.semantic.primary,
  },
  dayButtonText: {
    fontWeight: '500',
    color: designTokens.colors.semantic.text,
  },
  dayButtonTextActive: {
    color: designTokens.colors.semantic.surface,
    fontWeight: '600',
  },
  timeRow: {
    flexDirection: 'row',
    gap: designTokens.spacing.scale.md,
  },
  timeInput: {
    flex: 1,
  },
  timeLabel: {
    marginBottom: designTokens.spacing.scale.sm,
    color: designTokens.colors.semantic.textSecondary,
  },
  timeButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: designTokens.colors.semantic.surface,
    padding: designTokens.spacing.scale.md,
    borderRadius: componentTokens.card.default.borderRadius,
    borderWidth: 1,
    borderColor: designTokens.colors.semantic.border,
  },
  timeText: {
    marginLeft: designTokens.spacing.scale.sm,
    fontWeight: '500',
  },
  settingRow: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: designTokens.spacing.scale.md,
    borderBottomWidth: 1,
    borderBottomColor: designTokens.colors.semantic.border,
  },
  settingIcon: {
    marginRight: designTokens.spacing.scale.md,
  },
  settingContent: {
    flex: 1,
  },
  settingTitle: {
    fontWeight: '500',
    marginBottom: designTokens.spacing.scale.xs,
  },
  settingDescription: {
    color: designTokens.colors.semantic.textSecondary,
    marginBottom: designTokens.spacing.scale.xs,
  },
  settingValue: {
    color: designTokens.colors.semantic.primary,
    fontWeight: '500',
  },
  settingRight: {
    marginLeft: designTokens.spacing.scale.md,
  },
  actions: {
    marginTop: designTokens.spacing.scale.md,
  },
  resetButton: {
    alignSelf: 'center',
  },
});
