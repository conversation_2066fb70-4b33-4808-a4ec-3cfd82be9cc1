import React, { useState, useEffect } from 'react';
import {
  View,
  StyleSheet,
  Alert,
} from 'react-native';
import { useRouter, useLocalSearchParams } from 'expo-router';
import { RadialGradient } from '@/components/ui/RadialGradient';
import { DeclineReasonsModal } from '@/components/supplier/DeclineReasonsModal';
import { mockBookingRequests, BookingRequest } from '@/mocks/booking-requests';

export default function DeclineRequestScreen() {
  const router = useRouter();
  const { id } = useLocalSearchParams<{ id: string }>();
  const [request, setRequest] = useState<BookingRequest | null>(null);
  const [modalVisible, setModalVisible] = useState(true);

  useEffect(() => {
    const foundRequest = mockBookingRequests.find(req => req.id === id);
    setRequest(foundRequest || null);
  }, [id]);

  const handleDecline = async (reason: { category: string; message: string }) => {
    if (!request) return;

    try {
      // In a real app, this would call an API to decline the request
      console.log('Declining request:', {
        requestId: request.id,
        reason: reason.category,
        message: reason.message,
      });

      // Show success message
      Alert.alert(
        'Request Declined',
        `Your decline message has been sent to ${request.customerName}.`,
        [
          {
            text: 'OK',
            onPress: () => {
              setModalVisible(false);
              // Navigate back to requests list
              router.replace('/supplier/requests');
            },
          },
        ]
      );
    } catch (error) {
      Alert.alert(
        'Error',
        'Failed to decline the request. Please try again.',
        [{ text: 'OK' }]
      );
    }
  };

  const handleClose = () => {
    setModalVisible(false);
    router.back();
  };

  if (!request) {
    return (
      <RadialGradient variant="appBackground" style={styles.container}>
        {/* Empty container - will navigate back if request not found */}
      </RadialGradient>
    );
  }

  return (
    <RadialGradient variant="appBackground" style={styles.container}>
      <DeclineReasonsModal
        visible={modalVisible}
        onClose={handleClose}
        onDecline={handleDecline}
        customerName={request.customerName}
        serviceName={request.serviceName}
        requestDate={request.requestedDate}
        requestTime={request.requestedTime}
      />
    </RadialGradient>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
});
