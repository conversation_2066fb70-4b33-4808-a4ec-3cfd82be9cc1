import React, { useState, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  Animated,
  Alert,
  Platform,
} from 'react-native';
import { 
  Send, 
  Image as ImageIcon, 
  Mic, 
  Smile, 
  Plus,
  Camera,
  Paperclip,
  X,
} from 'lucide-react-native';
import { designTokens } from '@/constants/design-tokens';

interface EnhancedMessageInputProps {
  message: string;
  onMessageChange: (text: string) => void;
  onSend: () => void;
  onImageSelect: () => void;
  onVoiceRecord: (isRecording: boolean) => void;
  onEmojiPress: () => void;
  disabled?: boolean;
  isRecording?: boolean;
  recordingDuration?: number;
}

export const EnhancedMessageInput: React.FC<EnhancedMessageInputProps> = ({
  message,
  onMessageChange,
  onSend,
  onImageSelect,
  onVoiceRecord,
  onEmojiPress,
  disabled = false,
  isRecording = false,
  recordingDuration = 0,
}) => {
  const [showAttachments, setShowAttachments] = useState(false);
  const [inputHeight, setInputHeight] = useState(40);
  
  const attachmentAnimation = useRef(new Animated.Value(0)).current;
  const recordingAnimation = useRef(new Animated.Value(0)).current;
  const pulseAnimation = useRef(new Animated.Value(1)).current;

  React.useEffect(() => {
    if (showAttachments) {
      Animated.spring(attachmentAnimation, {
        toValue: 1,
        useNativeDriver: true,
      }).start();
    } else {
      Animated.spring(attachmentAnimation, {
        toValue: 0,
        useNativeDriver: true,
      }).start();
    }
  }, [showAttachments]);

  React.useEffect(() => {
    if (isRecording) {
      // Start recording animation
      Animated.timing(recordingAnimation, {
        toValue: 1,
        duration: 200,
        useNativeDriver: true,
      }).start();
      
      // Start pulse animation
      const pulse = () => {
        Animated.sequence([
          Animated.timing(pulseAnimation, {
            toValue: 1.2,
            duration: 600,
            useNativeDriver: true,
          }),
          Animated.timing(pulseAnimation, {
            toValue: 1,
            duration: 600,
            useNativeDriver: true,
          }),
        ]).start(() => {
          if (isRecording) pulse();
        });
      };
      pulse();
    } else {
      Animated.timing(recordingAnimation, {
        toValue: 0,
        duration: 200,
        useNativeDriver: true,
      }).start();
      pulseAnimation.stopAnimation();
      pulseAnimation.setValue(1);
    }
  }, [isRecording]);

  const handleAttachmentPress = () => {
    setShowAttachments(!showAttachments);
  };

  const handleVoicePress = () => {
    if (isRecording) {
      onVoiceRecord(false);
    } else {
      Alert.alert(
        'Voice Message',
        'Hold to record a voice message',
        [
          { text: 'Cancel', style: 'cancel' },
          { 
            text: 'Start Recording', 
            onPress: () => onVoiceRecord(true)
          },
        ]
      );
    }
  };

  const handleCameraPress = () => {
    setShowAttachments(false);
    Alert.alert(
      'Camera',
      'Take a photo to share',
      [
        { text: 'Cancel', style: 'cancel' },
        { text: 'Open Camera', onPress: onImageSelect },
      ]
    );
  };

  const handleGalleryPress = () => {
    setShowAttachments(false);
    onImageSelect();
  };

  const formatRecordingTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  const canSend = message.trim().length > 0 && !disabled;

  return (
    <View style={styles.container}>
      {/* Attachment Options */}
      <Animated.View style={[
        styles.attachmentContainer,
        {
          opacity: attachmentAnimation,
          transform: [{
            translateY: attachmentAnimation.interpolate({
              inputRange: [0, 1],
              outputRange: [50, 0],
            }),
          }],
        },
      ]}>
        <View style={styles.attachmentOptions}>
          <TouchableOpacity
            style={styles.attachmentOption}
            onPress={handleCameraPress}
            activeOpacity={0.8}
          >
            <View style={[styles.attachmentIcon, styles.cameraIcon]}>
              <Camera size={20} color={designTokens.colors.components.button.text} />
            </View>
            <Text style={styles.attachmentLabel}>Camera</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.attachmentOption}
            onPress={handleGalleryPress}
            activeOpacity={0.8}
          >
            <View style={[styles.attachmentIcon, styles.galleryIcon]}>
              <ImageIcon size={20} color={designTokens.colors.components.button.text} />
            </View>
            <Text style={styles.attachmentLabel}>Gallery</Text>
          </TouchableOpacity>
        </View>
      </Animated.View>

      {/* Recording Overlay */}
      {isRecording && (
        <Animated.View style={[
          styles.recordingOverlay,
          {
            opacity: recordingAnimation,
          },
        ]}>
          <View style={styles.recordingContent}>
            <Animated.View style={[
              styles.recordingIndicator,
              {
                transform: [{ scale: pulseAnimation }],
              },
            ]}>
              <View style={styles.recordingDot} />
            </Animated.View>
            <Text style={styles.recordingText}>
              Recording... {formatRecordingTime(recordingDuration)}
            </Text>
            <TouchableOpacity
              style={styles.recordingCancel}
              onPress={() => onVoiceRecord(false)}
            >
              <X size={20} color={designTokens.colors.semantic.error} />
            </TouchableOpacity>
          </View>
        </Animated.View>
      )}

      {/* Main Input Container */}
      <View style={styles.inputContainer}>
        {/* Left Actions */}
        <View style={styles.leftActions}>
          <TouchableOpacity
            style={styles.actionButton}
            onPress={handleAttachmentPress}
            activeOpacity={0.7}
          >
            {showAttachments ? (
              <X size={24} color={designTokens.colors.semantic.textSecondary} />
            ) : (
              <Plus size={24} color={designTokens.colors.semantic.textSecondary} />
            )}
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.actionButton}
            onPress={onEmojiPress}
            activeOpacity={0.7}
          >
            <Smile size={24} color={designTokens.colors.semantic.textSecondary} />
          </TouchableOpacity>
        </View>

        {/* Text Input */}
        <View style={styles.textInputContainer}>
          <TextInput
            style={[styles.textInput, { height: Math.max(40, inputHeight) }]}
            placeholder="Type a message..."
            placeholderTextColor={designTokens.colors.semantic.textSecondary}
            value={message}
            onChangeText={onMessageChange}
            multiline
            maxLength={1000}
            onContentSizeChange={(event) => {
              setInputHeight(event.nativeEvent.contentSize.height);
            }}
            editable={!disabled && !isRecording}
          />
        </View>

        {/* Right Actions */}
        <View style={styles.rightActions}>
          {!isRecording && (
            <TouchableOpacity
              style={styles.actionButton}
              onPress={handleVoicePress}
              activeOpacity={0.7}
            >
              <Mic size={24} color={designTokens.colors.semantic.textSecondary} />
            </TouchableOpacity>
          )}

          <TouchableOpacity
            style={[
              styles.sendButton,
              canSend ? styles.sendButtonActive : styles.sendButtonDisabled,
            ]}
            onPress={onSend}
            disabled={!canSend}
            activeOpacity={0.8}
          >
            <Send size={20} color={
              canSend 
                ? designTokens.colors.components.button.text
                : designTokens.colors.semantic.textSecondary
            } />
          </TouchableOpacity>
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: designTokens.colors.semantic.surface,
    borderTopWidth: 1,
    borderTopColor: designTokens.colors.semantic.border,
  },
  attachmentContainer: {
    position: 'absolute',
    bottom: '100%',
    left: 0,
    right: 0,
    backgroundColor: designTokens.colors.semantic.surface,
    borderTopWidth: 1,
    borderTopColor: designTokens.colors.semantic.border,
    ...designTokens.shadows.md,
  },
  attachmentOptions: {
    flexDirection: 'row',
    paddingHorizontal: designTokens.spacing.scale.lg,
    paddingVertical: designTokens.spacing.scale.md,
    gap: designTokens.spacing.scale.xl,
  },
  attachmentOption: {
    alignItems: 'center',
    gap: designTokens.spacing.scale.xs,
  },
  attachmentIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    ...designTokens.shadows.sm,
  },
  cameraIcon: {
    backgroundColor: designTokens.colors.semantic.primary,
  },
  galleryIcon: {
    backgroundColor: designTokens.colors.semantic.accent,
  },
  attachmentLabel: {
    fontSize: designTokens.typography.sizes.small,
    fontWeight: designTokens.typography.weights.medium,
    color: designTokens.colors.semantic.text,
  },
  recordingOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: designTokens.colors.semantic.error + '10',
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 10,
  },
  recordingContent: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: designTokens.spacing.scale.md,
    paddingHorizontal: designTokens.spacing.scale.lg,
    paddingVertical: designTokens.spacing.scale.md,
    backgroundColor: designTokens.colors.semantic.surface,
    borderRadius: designTokens.borderRadius.lg,
    ...designTokens.shadows.md,
  },
  recordingIndicator: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: designTokens.colors.semantic.error + '20',
    justifyContent: 'center',
    alignItems: 'center',
  },
  recordingDot: {
    width: 12,
    height: 12,
    borderRadius: 6,
    backgroundColor: designTokens.colors.semantic.error,
  },
  recordingText: {
    fontSize: designTokens.typography.sizes.small,
    fontWeight: designTokens.typography.weights.medium,
    color: designTokens.colors.semantic.text,
  },
  recordingCancel: {
    padding: designTokens.spacing.scale.sm,
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    paddingHorizontal: designTokens.spacing.scale.md,
    paddingVertical: designTokens.spacing.scale.sm,
    gap: designTokens.spacing.scale.sm,
  },
  leftActions: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: designTokens.spacing.scale.xs,
  },
  rightActions: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: designTokens.spacing.scale.xs,
  },
  actionButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  textInputContainer: {
    flex: 1,
    backgroundColor: designTokens.colors.semantic.background,
    borderRadius: designTokens.borderRadius.lg,
    borderWidth: 1,
    borderColor: designTokens.colors.semantic.border,
    paddingHorizontal: designTokens.spacing.scale.md,
    paddingVertical: designTokens.spacing.scale.sm,
    maxHeight: 120,
  },
  textInput: {
    fontSize: designTokens.typography.sizes.small,
    color: designTokens.colors.semantic.text,
    textAlignVertical: 'top',
    ...Platform.select({
      ios: {
        paddingTop: 8,
        paddingBottom: 8,
      },
      android: {
        paddingTop: 4,
        paddingBottom: 4,
      },
    }),
  },
  sendButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    ...designTokens.shadows.sm,
  },
  sendButtonActive: {
    backgroundColor: designTokens.colors.semantic.accent,
  },
  sendButtonDisabled: {
    backgroundColor: designTokens.colors.semantic.border,
  },
});
