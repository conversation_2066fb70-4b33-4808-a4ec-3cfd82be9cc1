import React, { useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  SafeAreaView,
  Alert,
} from 'react-native';
import { router, useLocalSearchParams } from 'expo-router';

import { RadialGradient } from '@/components/ui/RadialGradient';
import { ProgressBar } from '@/components/ui/ProgressBar';
import { useBookingStore } from '@/stores/booking-store';
import { designTokens } from '@/constants/design-tokens';

// Import step components (we'll create these next)
import { ServiceSelectionStep } from './steps/ServiceSelectionStep';
import { DateTimePickerStep } from './steps/DateTimePickerStep';
import { LocationSelectionStep } from './steps/LocationSelectionStep';
import { SpecialRequestsStep } from './steps/SpecialRequestsStep';
import { BookingSummaryStep } from './steps/BookingSummaryStep';
import { PaymentSelectionStep } from './steps/PaymentSelectionStep';
import { BookingConfirmationStep } from './steps/BookingConfirmationStep';

interface BookingWizardProps {
  companionId?: string;
  initialStep?: number;
}

const STEP_LABELS = [
  'Service',
  'Date & Time',
  'Location',
  'Requests',
  'Summary',
  'Payment',
  'Confirmation',
];

export const BookingWizard: React.FC<BookingWizardProps> = ({
  companionId,
  initialStep = 1,
}) => {
  const { companionId: paramCompanionId } = useLocalSearchParams();
  const {
    bookingData,
    isLoading,
    error,
    nextStep,
    prevStep,
    goToStep,
    setCompanionId,
    resetBooking,
  } = useBookingStore();

  const currentCompanionId = companionId || (paramCompanionId as string);

  useEffect(() => {
    if (currentCompanionId) {
      setCompanionId(currentCompanionId);
    }
    if (initialStep !== 1) {
      goToStep(initialStep);
    }
  }, [currentCompanionId, initialStep]);

  const handleNext = () => {
    nextStep();
  };

  const handlePrevious = () => {
    if (bookingData.currentStep > 1) {
      prevStep();
    }
  };

  const handleClose = () => {
    router.back();
  };

  const renderCurrentStep = () => {
    switch (bookingData.currentStep) {
      case 1:
        return <ServiceSelectionStep onNext={handleNext} />;
      case 2:
        return <DateTimePickerStep onNext={handleNext} onPrevious={handlePrevious} />;
      case 3:
        return <LocationSelectionStep onNext={handleNext} onPrevious={handlePrevious} />;
      case 4:
        return <SpecialRequestsStep onNext={handleNext} onPrevious={handlePrevious} />;
      case 5:
        return <BookingSummaryStep onNext={handleNext} onPrevious={handlePrevious} />;
      case 6:
        return <BookingConfirmationStep onPrevious={handlePrevious} />;
      default:
        return <ServiceSelectionStep onNext={handleNext} />;
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <RadialGradient style={styles.gradient}>
        <View style={styles.progressContainer}>
          <ProgressBar
            currentStep={bookingData.currentStep}
            totalSteps={STEP_LABELS.length}
            labels={STEP_LABELS}
            showLabels={false}
            variant="gradient"
          />
        </View>

        <View style={styles.content}>
          {renderCurrentStep()}
        </View>
      </RadialGradient>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  gradient: {
    flex: 1,
  },
  progressContainer: {
    paddingHorizontal: designTokens.spacing.scale.xl,
    paddingVertical: designTokens.spacing.scale.lg,
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
    borderBottomWidth: 1,
    borderBottomColor: designTokens.colors.semantic.border + '30',
  },
  content: {
    flex: 1,
  },
});
