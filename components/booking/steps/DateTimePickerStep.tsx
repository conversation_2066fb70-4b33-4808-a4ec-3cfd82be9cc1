import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
} from 'react-native';
import { Calendar, Clock, AlertCircle } from 'lucide-react-native';
import { Card } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { BookingStepFooter } from '../BookingStepFooter';
import { useBookingStore, BookingDateTime } from '@/stores/booking-store';
import { designTokens } from '@/constants/design-tokens';
import { useCompanionWeeklyAvailability } from '@/app/api/companion/companion';

interface TimeSlot {
  start: string;
  end: string;
  available: boolean;
  price?: number;
}

interface DayAvailability {
  date: string;
  available: boolean;
  slots: TimeSlot[];
}

interface DateTimePickerStepProps {
  onNext: () => void;
  onPrevious: () => void;
}

// Helper function to generate time slots
const generateTimeSlots = (startTime: string, endTime: string): TimeSlot[] => {
  const slots: TimeSlot[] = [];
  const start = new Date(`2000-01-01T${startTime}`);
  const end = new Date(`2000-01-01T${endTime}`);
  
  // Generate slots with 1-hour duration
  while (start < end) {
    const slotStart = start.toTimeString().slice(0, 5);
    start.setHours(start.getHours() + 1);
    const slotEnd = start.toTimeString().slice(0, 5);
    
    if (start <= end) {
      slots.push({
        start: slotStart,
        end: slotEnd,
        available: true,
        price: 0 // You can set this based on your requirements
      });
    }
  }
  
  return slots;
};

export const DateTimePickerStep: React.FC<DateTimePickerStepProps> = ({
  onNext,
  onPrevious,
}) => {
  const { bookingData, updateDateTime } = useBookingStore();
  const [selectedDate, setSelectedDate] = useState<string | null>(
    bookingData.dateTime?.date || null
  );
  const [selectedTime, setSelectedTime] = useState<string | null>(
    bookingData.dateTime?.time || null
  );

  // Get companion ID from booking data
  const companionId = bookingData.companionId;

  // Fetch availability data using the API hook
  const { data: availabilityData, isLoading: isLoadingAvailability } = useCompanionWeeklyAvailability(
    companionId,
    '00:00', // Start time
    '23:59'  // End time
  );

  console.log('companionId:', companionId);
  console.log('Raw Availability Data:', availabilityData);

  // Get available dates and time slots from API response
  const availableDates = availabilityData?.success && availabilityData.data.availability 
    ? availabilityData.data.availability.filter(day => day.available)
    : [];

  console.log('Filtered Available Dates:', availableDates);

  // Get time slots for selected date
  const selectedDateData = availableDates.find(d => d.date === selectedDate);
  console.log('Selected Date Data:', selectedDateData);
  
  // Generate hourly slots from the available time range
  const availableTimeSlots = selectedDateData?.slots?.flatMap(slot => 
    generateTimeSlots(slot.start, slot.end)
  ) || [];
  
  console.log('Available Time Slots:', availableTimeSlots);

  const handleDateSelect = (date: string) => {
    console.log('Selected Date:', date);
    setSelectedDate(date);
    setSelectedTime(null); // Reset time when date changes
  };

  const handleTimeSelect = (slot: { start: string; end: string; available: boolean; price?: number }) => {
    console.log('Selected Time Slot:', slot);
    if (!selectedDate || !slot.available) return;
    
    setSelectedTime(slot.start);
    
    // Calculate duration in hours
    const startTime = new Date(`${selectedDate}T${slot.start}`);
    const endTime = new Date(`${selectedDate}T${slot.end}`);
    const durationHours = (endTime.getTime() - startTime.getTime()) / (1000 * 60 * 60);
    
    const dateTimeData: BookingDateTime = {
      date: selectedDate,
      time: slot.start,
      duration: durationHours,
      endTime: slot.end,
      isAvailable: true,
    };
    
    console.log('Updated DateTime Data:', dateTimeData);
    updateDateTime(dateTimeData);
  };

  const handleNext = () => {
    if (selectedDate && selectedTime) {
      onNext();
    } else {
      Alert.alert('Selection Required', 'Please select both date and time to continue.');
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      weekday: 'short',
      month: 'short',
      day: 'numeric',
    });
  };

  const formatDateLong = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      weekday: 'long',
      month: 'long',
      day: 'numeric',
      year: 'numeric',
    });
  };

  return (
    <View style={styles.container}>
      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        <View style={styles.header}>
          <Text style={styles.title}>When would you like to meet?</Text>
          <Text style={styles.subtitle}>
            Select your preferred date and starting time
          </Text>
        </View>

        {/* Date Selection */}
        <Card style={styles.sectionCard}>
          <View style={styles.sectionHeader}>
            <Calendar size={20} color={designTokens.colors.semantic.primary} />
            <Text style={styles.sectionTitle}>Choose Date</Text>
          </View>
          
          {isLoadingAvailability ? (
            <View style={styles.loadingContainer}>
              <Text style={styles.loadingText}>Loading availability...</Text>
            </View>
          ) : availableDates.length > 0 ? (
          <ScrollView 
            horizontal 
            showsHorizontalScrollIndicator={false}
            style={styles.dateScrollView}
            contentContainerStyle={styles.dateScrollContent}
          >
              {availableDates.map((date, index) => {
                const dateObj = new Date(date.date);
                return (
              <TouchableOpacity
                    key={date.date}
                style={[
                  styles.dateOption,
                      selectedDate === date.date && styles.selectedDateOption,
                ]}
                    onPress={() => handleDateSelect(date.date)}
              >
                <Text style={[
                  styles.dateLabel,
                      selectedDate === date.date && styles.selectedDateLabel,
                ]}>
                      {dateObj.toLocaleDateString('en-US', { weekday: 'short', month: 'short' })}
                </Text>
                <Text style={[
                  styles.dateNumber,
                      selectedDate === date.date && styles.selectedDateNumber,
                ]}>
                      {dateObj.getDate()}
                </Text>
              </TouchableOpacity>
                );
              })}
          </ScrollView>
          ) : (
            <View style={styles.noTimesContainer}>
              <Text style={styles.noTimesText}>No available dates found</Text>
            </View>
          )}
        </Card>

        {/* Time Selection */}
        {selectedDate && (
          <Card style={styles.sectionCard}>
            <View style={styles.sectionHeader}>
              <Clock size={20} color={designTokens.colors.semantic.primary} />
              <Text style={styles.sectionTitle}>Choose Time</Text>
            </View>
            
            <Text style={styles.sectionSubtitle}>
              Available times for {formatDateLong(selectedDate)}
            </Text>
            
            <View style={styles.timeGrid}>
              {availableTimeSlots.length > 0 ? (
                availableTimeSlots.map((slot, index) => (
                <TouchableOpacity
                  key={index}
                  style={[
                    styles.timeOption,
                      selectedTime === slot.start && styles.selectedTimeOption,
                  ]}
                    onPress={() => handleTimeSelect(slot)}
                >
                  <Text style={[
                    styles.timeText,
                      selectedTime === slot.start && styles.selectedTimeText,
                  ]}>
                      {slot.start.slice(0, 5)}
                  </Text>
                  </TouchableOpacity>
                ))
              ) : (
                <View style={styles.noTimesContainer}>
                  <Text style={styles.noTimesText}>No available time slots for this date</Text>
                </View>
              )}
            </View>
          </Card>
        )}

        {/* Duration Info */}
        {selectedTime && selectedDate && (
          <Card style={styles.sectionCard}>
            <View style={styles.durationInfo}>
              <View style={styles.durationHeader}>
                <AlertCircle size={20} color={designTokens.colors.semantic.accent} />
                <Text style={styles.durationTitle}>Duration Information</Text>
              </View>
              
              <View style={styles.durationDetails}>
                <View style={styles.durationRow}>
                  <Text style={styles.durationLabel}>Start Time:</Text>
                  <Text style={styles.durationValue}>{selectedTime}</Text>
                </View>
                <View style={styles.durationRow}>
                  <Text style={styles.durationLabel}>Duration:</Text>
                  <Text style={styles.durationValue}>
                    {bookingData.dateTime?.duration || bookingData.service?.duration || 0} hours
                  </Text>
                </View>
                <View style={styles.durationRow}>
                  <Text style={styles.durationLabel}>End Time:</Text>
                  <Text style={styles.durationValue}>
                    {bookingData.dateTime?.endTime || 'N/A'}
                  </Text>
                </View>
              </View>
              
              <Text style={styles.durationNote}>
                💡 Your companion will be available for the full duration of the selected service.
              </Text>
            </View>
          </Card>
        )}
      </ScrollView>

      <BookingStepFooter
        onPrevious={onPrevious}
        onNext={handleNext}
        nextTitle="Continue"
        nextDisabled={!selectedDate || !selectedTime}
        showPrevious={true}
        showNext={true}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: designTokens.colors.semantic.background,
  },
  content: {
    flex: 1,
    paddingHorizontal: designTokens.spacing.scale.lg,
  },
  header: {
    paddingVertical: designTokens.spacing.scale.xl,
    alignItems: 'center',
  },
  title: {
    ...designTokens.typography.styles.heading,
    color: designTokens.colors.semantic.text,
    textAlign: 'center',
    marginBottom: designTokens.spacing.scale.sm,
  },
  subtitle: {
    ...designTokens.typography.styles.body,
    color: designTokens.colors.semantic.textSecondary,
    textAlign: 'center',
    lineHeight: designTokens.typography.lineHeights.normal * designTokens.typography.sizes.body,
  },
  sectionCard: {
    marginBottom: designTokens.spacing.scale.lg,
    borderRadius: designTokens.borderRadius.components.card,
    ...designTokens.shadows.md,
  },
  sectionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: designTokens.spacing.scale.sm,
    marginBottom: designTokens.spacing.scale.md,
  },
  sectionTitle: {
    ...designTokens.typography.styles.subheading,
    color: designTokens.colors.semantic.text,
  },
  sectionSubtitle: {
    ...designTokens.typography.styles.caption,
    color: designTokens.colors.semantic.textSecondary,
    marginBottom: designTokens.spacing.scale.md,
    lineHeight: designTokens.typography.lineHeights.normal * designTokens.typography.sizes.caption,
  },
  dateScrollView: {
    marginHorizontal: -designTokens.spacing.scale.sm,
  },
  dateScrollContent: {
    paddingHorizontal: designTokens.spacing.scale.sm,
    gap: designTokens.spacing.scale.sm,
  },
  dateOption: {
    alignItems: 'center',
    paddingVertical: designTokens.spacing.scale.md,
    paddingHorizontal: designTokens.spacing.scale.lg,
    borderRadius: designTokens.borderRadius.components.button,
    borderWidth: 1,
    borderColor: designTokens.colors.semantic.border,
    backgroundColor: designTokens.colors.semantic.surface,
    minWidth: 80,
    minHeight: 44, // Accessibility touch target
    justifyContent: 'center',
    ...designTokens.shadows.sm,
  },
  selectedDateOption: {
    borderColor: designTokens.colors.semantic.primary,
    backgroundColor: designTokens.colors.semantic.primary,
    ...designTokens.shadows.md,
    transform: [{ scale: 1.05 }],
  },
  dateLabel: {
    ...designTokens.typography.styles.caption,
    fontWeight: designTokens.typography.weights.medium,
    color: designTokens.colors.semantic.textSecondary,
    marginBottom: designTokens.spacing.scale.xs,
    fontSize: designTokens.typography.sizes.small,
  },
  selectedDateLabel: {
    color: designTokens.colors.semantic.surface,
  },
  dateNumber: {
    fontSize: designTokens.typography.sizes.large,
    fontWeight: designTokens.typography.weights.bold,
    color: designTokens.colors.semantic.text,
  },
  selectedDateNumber: {
    color: designTokens.colors.semantic.surface,
  },
  timeGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: designTokens.spacing.scale.sm,
    marginTop: designTokens.spacing.scale.md,
  },
  timeOption: {
    width: '30%',
    paddingVertical: designTokens.spacing.scale.md,
    paddingHorizontal: designTokens.spacing.scale.sm,
    borderRadius: designTokens.borderRadius.components.button,
    borderWidth: 1,
    borderColor: designTokens.colors.semantic.border,
    backgroundColor: designTokens.colors.semantic.surface,
    justifyContent: 'center',
    alignItems: 'center',
    minHeight: 48,
    ...designTokens.shadows.sm,
  },
  selectedTimeOption: {
    borderColor: designTokens.colors.semantic.primary,
    backgroundColor: designTokens.colors.semantic.primary,
    ...designTokens.shadows.md,
  },
  timeText: {
    ...designTokens.typography.styles.body,
    color: designTokens.colors.semantic.text,
    fontWeight: designTokens.typography.weights.medium,
  },
  selectedTimeText: {
    color: designTokens.colors.semantic.surface,
  },
  priceText: {
    ...designTokens.typography.styles.caption,
    color: designTokens.colors.semantic.textSecondary,
    marginTop: designTokens.spacing.scale.xs,
  },
  noTimesContainer: {
    width: '100%',
    paddingVertical: designTokens.spacing.scale.xl,
    justifyContent: 'center',
    alignItems: 'center',
  },
  noTimesText: {
    ...designTokens.typography.styles.body,
    color: designTokens.colors.semantic.textSecondary,
    textAlign: 'center',
  },
  durationInfo: {
    backgroundColor: designTokens.colors.semantic.accent + '10',
    borderRadius: designTokens.borderRadius.components.card,
    padding: designTokens.spacing.scale.lg,
    borderWidth: 1,
    borderColor: designTokens.colors.semantic.accent + '20',
  },
  durationHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: designTokens.spacing.scale.sm,
    marginBottom: designTokens.spacing.scale.md,
  },
  durationTitle: {
    ...designTokens.typography.styles.subheading,
    color: designTokens.colors.semantic.text,
    fontSize: designTokens.typography.sizes.body,
  },
  durationDetails: {
    marginBottom: designTokens.spacing.scale.md,
  },
  durationRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: designTokens.spacing.scale.sm,
  },
  durationLabel: {
    ...designTokens.typography.styles.caption,
    color: designTokens.colors.semantic.textSecondary,
  },
  durationValue: {
    ...designTokens.typography.styles.caption,
    fontWeight: designTokens.typography.weights.semibold,
    color: designTokens.colors.semantic.text,
  },
  durationNote: {
    ...designTokens.typography.styles.caption,
    color: designTokens.colors.semantic.textSecondary,
    fontStyle: 'italic',
    lineHeight: designTokens.typography.lineHeights.normal * designTokens.typography.sizes.caption,
  },
  footer: {
    padding: designTokens.spacing.scale.lg,
    backgroundColor: designTokens.colors.semantic.surface,
    borderTopWidth: 1,
    borderTopColor: designTokens.colors.semantic.border,
  },
  footerButtons: {
    flexDirection: 'row',
    gap: designTokens.spacing.scale.md,
  },
  previousButton: {
    flex: 1,
  },
  nextButton: {
    flex: 2,
  },
  loadingContainer: {
    paddingVertical: designTokens.spacing.scale.xl,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    ...designTokens.typography.styles.body,
    color: designTokens.colors.semantic.textSecondary,
  },
});
