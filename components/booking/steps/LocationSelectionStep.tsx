import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
} from 'react-native';
import { MapPin, Navigation, Clock, Star } from 'lucide-react-native';
import { Card } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { BookingStepFooter } from '../BookingStepFooter';
import { FormField } from '@/components/ui/FormField';
import { useBookingStore, BookingLocation } from '@/stores/booking-store';
import { designTokens, componentTokens } from '@/constants/design-tokens';

interface LocationSelectionStepProps {
  onNext: () => void;
  onPrevious: () => void;
}

// Mock popular locations data
const POPULAR_AREAS = [
  {
    id: 'sukhumvit',
    name: 'Sukhumvit',
    description: 'Shopping, dining, and nightlife district',
    estimatedDistance: 5.2,
    travelTime: 15,
    popularity: 4.8,
  },
  {
    id: 'silom',
    name: 'Silom',
    description: 'Business district with street food',
    estimatedDistance: 7.1,
    travelTime: 20,
    popularity: 4.6,
  },
  {
    id: 'chatuchak',
    name: '<PERSON><PERSON><PERSON><PERSON>',
    description: 'Weekend market and park area',
    estimatedDistance: 12.3,
    travelTime: 35,
    popularity: 4.9,
  },
  {
    id: 'khao_san',
    name: 'Khao San Road',
    description: 'Backpacker area with local culture',
    estimatedDistance: 8.7,
    travelTime: 25,
    popularity: 4.4,
  },
  {
    id: 'thonglor',
    name: 'Thonglor',
    description: 'Trendy area with cafes and galleries',
    estimatedDistance: 6.8,
    travelTime: 18,
    popularity: 4.7,
  },
  {
    id: 'old_city',
    name: 'Old City (Rattanakosin)',
    description: 'Historic temples and royal palace',
    estimatedDistance: 9.5,
    travelTime: 30,
    popularity: 4.9,
  },
];

const POPULAR_MEETING_POINTS = {
  sukhumvit: [
    'BTS Asok Station Exit 1',
    'Terminal 21 Main Entrance',
    'Soi Cowboy Entrance',
    'Benchasiri Park',
  ],
  silom: [
    'BTS Sala Daeng Station',
    'Patpong Night Market',
    'Lumpini Park Main Gate',
    'Silom Complex',
  ],
  chatuchak: [
    'Chatuchak Weekend Market Gate 1',
    'BTS Mo Chit Station',
    'Chatuchak Park',
    'JJ Green Night Market',
  ],
  khao_san: [
    'Khao San Road Center',
    'Democracy Monument',
    'Wat Chana Songkhram',
    'Rambuttri Village',
  ],
  thonglor: [
    'BTS Thong Lo Station Exit 3',
    'The Commons Thonglor',
    'J Avenue',
    'Thonglor Soi 10',
  ],
  old_city: [
    'Wat Pho Temple Main Entrance',
    'Grand Palace Ticket Office',
    'Wat Arun Pier',
    'Sanam Luang',
  ],
};

export const LocationSelectionStep: React.FC<LocationSelectionStepProps> = ({
  onNext,
  onPrevious,
}) => {
  const { bookingData, updateLocation } = useBookingStore();
  const [selectedArea, setSelectedArea] = useState<string>(
    bookingData.location?.area || ''
  );
  const [selectedMeetingPoint, setSelectedMeetingPoint] = useState<string>(
    bookingData.location?.meetingPoint || ''
  );
  const [customMeetingPoint, setCustomMeetingPoint] = useState<string>('');
  const [useCustomLocation, setUseCustomLocation] = useState<boolean>(false);

  const handleAreaSelect = (areaId: string) => {
    setSelectedArea(areaId);
    setSelectedMeetingPoint(''); // Reset meeting point when area changes
    setUseCustomLocation(false);
  };

  const handleMeetingPointSelect = (meetingPoint: string) => {
    setSelectedMeetingPoint(meetingPoint);
    setUseCustomLocation(false);
    setCustomMeetingPoint('');
  };

  const handleCustomMeetingPoint = () => {
    setUseCustomLocation(true);
    setSelectedMeetingPoint('');
  };

  const handleNext = () => {
    const finalMeetingPoint = useCustomLocation ? customMeetingPoint : selectedMeetingPoint;
    
    if (!selectedArea || (!finalMeetingPoint && !useCustomLocation)) {
      Alert.alert('Selection Required', 'Please select an area and meeting point to continue.');
      return;
    }

    if (useCustomLocation && !customMeetingPoint.trim()) {
      Alert.alert('Meeting Point Required', 'Please enter a custom meeting point.');
      return;
    }

    const selectedAreaData = POPULAR_AREAS.find(area => area.id === selectedArea);
    
    const locationData: BookingLocation = {
      area: selectedAreaData?.name || selectedArea,
      meetingPoint: finalMeetingPoint,
      estimatedDistance: selectedAreaData?.estimatedDistance,
      travelTime: selectedAreaData?.travelTime,
    };

    updateLocation(locationData);
    onNext();
  };

  const AreaCard: React.FC<{ area: typeof POPULAR_AREAS[0] }> = ({ area }) => {
    const isSelected = selectedArea === area.id;

    return (
      <TouchableOpacity
        style={[
          styles.areaCard,
          isSelected && styles.selectedAreaCard,
        ]}
        onPress={() => handleAreaSelect(area.id)}
      >
        <View style={styles.areaHeader}>
          <View style={styles.areaInfo}>
            <Text style={styles.areaName}>{area.name}</Text>
            <Text style={styles.areaDescription}>{area.description}</Text>
          </View>
          <View style={styles.areaStats}>
            <View style={styles.statRow}>
              <Navigation size={14} color={designTokens.colors.semantic.textSecondary} />
              <Text style={styles.statText}>{area.estimatedDistance} km</Text>
            </View>
            <View style={styles.statRow}>
              <Clock size={14} color={designTokens.colors.semantic.textSecondary} />
              <Text style={styles.statText}>{area.travelTime} min</Text>
            </View>
            <View style={styles.statRow}>
              <Star size={14} color={designTokens.colors.semantic.accent} />
              <Text style={styles.statText}>{area.popularity}</Text>
            </View>
          </View>
        </View>
      </TouchableOpacity>
    );
  };

  return (
    <View style={styles.container}>
      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        <View style={styles.header}>
          <Text style={styles.title}>Where shall we meet?</Text>
          <Text style={styles.subtitle}>
            Choose an area and specific meeting point
          </Text>
        </View>

        {/* Area Selection */}
        <Card style={styles.sectionCard} padding={16}>
          <View style={styles.sectionHeader}>
            <MapPin size={20} color={designTokens.colors.semantic.primary} />
            <Text style={styles.sectionTitle}>Choose Area</Text>
          </View>
          
          <View style={styles.areasContainer}>
            {POPULAR_AREAS.map((area) => (
              <AreaCard key={area.id} area={area} />
            ))}
          </View>
        </Card>

        {/* Meeting Point Selection */}
        {selectedArea && (
          <Card style={styles.sectionCard} padding={16}>
            <View style={styles.sectionHeader}>
              <Navigation size={20} color={designTokens.colors.semantic.primary} />
              <Text style={styles.sectionTitle}>Meeting Point</Text>
            </View>
            
            <Text style={styles.sectionSubtitle}>
              Popular meeting points in {POPULAR_AREAS.find(a => a.id === selectedArea)?.name}
            </Text>
            
            <View style={styles.meetingPointsContainer}>
              {POPULAR_MEETING_POINTS[selectedArea as keyof typeof POPULAR_MEETING_POINTS]?.map((point, index) => (
                <TouchableOpacity
                  key={index}
                  style={[
                    styles.meetingPointOption,
                    selectedMeetingPoint === point && styles.selectedMeetingPointOption,
                  ]}
                  onPress={() => handleMeetingPointSelect(point)}
                >
                  <Text style={[
                    styles.meetingPointText,
                    selectedMeetingPoint === point && styles.selectedMeetingPointText,
                  ]}>
                    {point}
                  </Text>
                </TouchableOpacity>
              ))}
              
              <TouchableOpacity
                style={[
                  styles.meetingPointOption,
                  styles.customMeetingPointOption,
                  useCustomLocation && styles.selectedMeetingPointOption,
                ]}
                onPress={handleCustomMeetingPoint}
              >
                <Text style={[
                  styles.meetingPointText,
                  styles.customMeetingPointText,
                  useCustomLocation && styles.selectedMeetingPointText,
                ]}>
                  + Custom Location
                </Text>
              </TouchableOpacity>
            </View>

            {useCustomLocation && (
              <View style={styles.customLocationContainer}>
                <FormField
                  label="Custom Meeting Point"
                  placeholder="Enter specific address or landmark..."
                  value={customMeetingPoint}
                  onChangeText={setCustomMeetingPoint}
                  multiline
                  numberOfLines={3}
                />
                <Text style={styles.customLocationNote}>
                  💡 Be as specific as possible to help your companion find you easily.
                </Text>
              </View>
            )}
          </Card>
        )}

        {/* Location Summary */}
        {selectedArea && (selectedMeetingPoint || (useCustomLocation && customMeetingPoint)) && (
          <Card style={styles.sectionCard} padding={16}>
            <View style={styles.summaryHeader}>
              <Text style={styles.summaryTitle}>Meeting Details</Text>
            </View>
            
            <View style={styles.summaryContent}>
              <View style={styles.summaryRow}>
                <Text style={styles.summaryLabel}>Area:</Text>
                <Text style={styles.summaryValue}>
                  {POPULAR_AREAS.find(a => a.id === selectedArea)?.name}
                </Text>
              </View>
              <View style={styles.summaryRow}>
                <Text style={styles.summaryLabel}>Meeting Point:</Text>
                <Text style={styles.summaryValue}>
                  {useCustomLocation ? customMeetingPoint : selectedMeetingPoint}
                </Text>
              </View>
              {!useCustomLocation && (
                <>
                  <View style={styles.summaryRow}>
                    <Text style={styles.summaryLabel}>Distance:</Text>
                    <Text style={styles.summaryValue}>
                      ~{POPULAR_AREAS.find(a => a.id === selectedArea)?.estimatedDistance} km
                    </Text>
                  </View>
                  <View style={styles.summaryRow}>
                    <Text style={styles.summaryLabel}>Travel Time:</Text>
                    <Text style={styles.summaryValue}>
                      ~{POPULAR_AREAS.find(a => a.id === selectedArea)?.travelTime} minutes
                    </Text>
                  </View>
                </>
              )}
            </View>
          </Card>
        )}
      </ScrollView>

      <BookingStepFooter
        onPrevious={onPrevious}
        onNext={handleNext}
        nextTitle="Continue"
        nextDisabled={!selectedArea || (!selectedMeetingPoint && !useCustomLocation)}
        showPrevious={true}
        showNext={true}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: designTokens.colors.semantic.background,
  },
  content: {
    flex: 1,
    paddingHorizontal: designTokens.spacing.scale.lg,
  },
  header: {
    paddingVertical: designTokens.spacing.scale.xl,
    alignItems: 'center',
  },
  title: {
    ...designTokens.typography.styles.heading,
    color: designTokens.colors.semantic.text,
    textAlign: 'center',
    marginBottom: designTokens.spacing.scale.sm,
  },
  subtitle: {
    ...designTokens.typography.styles.body,
    color: designTokens.colors.semantic.textSecondary,
    textAlign: 'center',
    lineHeight: designTokens.typography.lineHeights.normal * designTokens.typography.sizes.body,
  },
  sectionCard: {
    marginBottom: designTokens.spacing.scale.lg,
    borderRadius: designTokens.borderRadius.components.card,
    ...designTokens.shadows.md,
  },
  sectionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: designTokens.spacing.scale.sm,
    marginBottom: designTokens.spacing.scale.md,
  },
  sectionTitle: {
    ...designTokens.typography.styles.subheading,
    color: designTokens.colors.semantic.text,
  },
  sectionSubtitle: {
    ...designTokens.typography.styles.caption,
    color: designTokens.colors.semantic.textSecondary,
    marginBottom: designTokens.spacing.scale.md,
    lineHeight: designTokens.typography.lineHeights.normal * designTokens.typography.sizes.caption,
  },
  areasContainer: {
    gap: designTokens.spacing.scale.sm,
  },
  areaCard: {
    padding: designTokens.spacing.scale.lg,
    borderRadius: designTokens.borderRadius.components.button,
    borderWidth: 1,
    borderColor: designTokens.colors.semantic.border,
    backgroundColor: designTokens.colors.semantic.surface,
    minHeight: 44, // Accessibility touch target
    ...designTokens.shadows.sm,
  },
  selectedAreaCard: {
    borderColor: designTokens.colors.semantic.primary,
    backgroundColor: designTokens.colors.semantic.primary + '10',
    ...designTokens.shadows.md,
    transform: [{ scale: 1.02 }],
  },
  areaHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
  },
  areaInfo: {
    flex: 1,
    marginRight: designTokens.spacing.scale.md,
  },
  areaName: {
    ...designTokens.typography.styles.subheading,
    color: designTokens.colors.semantic.text,
    marginBottom: designTokens.spacing.scale.xs,
    fontSize: designTokens.typography.sizes.body,
  },
  areaDescription: {
    ...designTokens.typography.styles.caption,
    color: designTokens.colors.semantic.textSecondary,
    lineHeight: designTokens.typography.lineHeights.normal * designTokens.typography.sizes.caption,
  },
  areaStats: {
    alignItems: 'flex-end',
    gap: designTokens.spacing.scale.xs,
  },
  statRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: designTokens.spacing.scale.xs,
  },
  statText: {
    ...designTokens.typography.styles.caption,
    color: designTokens.colors.semantic.textSecondary,
    fontWeight: designTokens.typography.weights.medium,
    fontSize: designTokens.typography.sizes.small,
  },
  meetingPointsContainer: {
    gap: designTokens.spacing.scale.sm,
  },
  meetingPointOption: {
    paddingVertical: designTokens.spacing.scale.md,
    paddingHorizontal: designTokens.spacing.scale.lg,
    borderRadius: designTokens.borderRadius.components.button,
    borderWidth: 1,
    borderColor: designTokens.colors.semantic.border,
    backgroundColor: designTokens.colors.semantic.surface,
    minHeight: 44, // Accessibility touch target
    justifyContent: 'center',
    ...designTokens.shadows.sm,
  },
  selectedMeetingPointOption: {
    borderColor: designTokens.colors.semantic.primary,
    backgroundColor: designTokens.colors.semantic.primary,
    ...designTokens.shadows.md,
    transform: [{ scale: 1.02 }],
  },
  customMeetingPointOption: {
    borderStyle: 'dashed',
    borderColor: designTokens.colors.semantic.accent,
  },
  meetingPointText: {
    ...designTokens.typography.styles.body,
    fontWeight: designTokens.typography.weights.medium,
    color: designTokens.colors.semantic.text,
    textAlign: 'center',
  },
  selectedMeetingPointText: {
    color: designTokens.colors.semantic.surface,
  },
  customMeetingPointText: {
    color: designTokens.colors.semantic.accent,
  },
  customLocationContainer: {
    marginTop: designTokens.spacing.scale.md,
    padding: designTokens.spacing.scale.lg,
    backgroundColor: designTokens.colors.semantic.accent + '10',
    borderRadius: designTokens.borderRadius.components.card,
    borderWidth: 1,
    borderColor: designTokens.colors.semantic.accent + '20',
  },
  customLocationNote: {
    ...designTokens.typography.styles.caption,
    color: designTokens.colors.semantic.textSecondary,
    fontStyle: 'italic',
    marginTop: designTokens.spacing.scale.sm,
    lineHeight: designTokens.typography.lineHeights.normal * designTokens.typography.sizes.caption,
  },
  summaryHeader: {
    marginBottom: designTokens.spacing.scale.md,
  },
  summaryTitle: {
    ...designTokens.typography.styles.subheading,
    color: designTokens.colors.semantic.text,
    fontSize: designTokens.typography.sizes.body,
  },
  summaryContent: {
    backgroundColor: designTokens.colors.semantic.primary + '10',
    borderRadius: designTokens.borderRadius.components.card,
    padding: designTokens.spacing.scale.lg,
    borderWidth: 1,
    borderColor: designTokens.colors.semantic.primary + '20',
  },
  summaryRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: designTokens.spacing.scale.sm,
  },
  summaryLabel: {
    ...designTokens.typography.styles.caption,
    color: designTokens.colors.semantic.textSecondary,
    flex: 1,
  },
  summaryValue: {
    ...designTokens.typography.styles.caption,
    fontWeight: designTokens.typography.weights.semibold,
    color: designTokens.colors.semantic.text,
    flex: 2,
    textAlign: 'right',
  },
  footer: {
    padding: designTokens.spacing.scale.lg,
    backgroundColor: designTokens.colors.semantic.surface,
    borderTopWidth: 1,
    borderTopColor: designTokens.colors.semantic.border,
    ...designTokens.shadows.sm,
  },
  footerButtons: {
    flexDirection: 'row',
    gap: designTokens.spacing.scale.md,
  },
  previousButton: {
    flex: 1,
  },
  nextButton: {
    flex: 2,
  },
});
