import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Animated,
} from 'react-native';
import { 
  CheckCircle, 
  Calendar, 
  Clock, 
  MapPin, 
  MessageCircle,
  Phone,
  Home,
  Share,
  Download,
} from 'lucide-react-native';
import { router } from 'expo-router';
import { Card } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { ProfileImage } from '@/components/ui/ProfileImage';
import { useBookingStore } from '@/stores/booking-store';
import { useCreateBooking } from '@/app/api/booking/booking';
import { designTokens } from '@/constants/design-tokens';
import { CompanionData } from '@/types/companion';

interface BookingConfirmationStepProps {
  onPrevious: () => void;
}

export const BookingConfirmationStep: React.FC<BookingConfirmationStepProps> = ({
  onPrevious,
}) => {
  const { bookingData, resetBooking } = useBookingStore();
  const [animationValue] = useState(new Animated.Value(0));
  const createBookingMutation = useCreateBooking();

  useEffect(() => {
    // Start success animation
    Animated.sequence([
      Animated.timing(animationValue, {
        toValue: 1,
        duration: 800,
        useNativeDriver: true,
      }),
    ]).start();
  }, []);

  // Early return if no companion data
  if (!bookingData.companionData) {
    return (
      <View style={styles.errorContainer}>
        <Text style={styles.errorText}>No companion data available</Text>
      </View>
    );
  }

  const handleMessageCompanion = () => {
    router.push(`/chat/${bookingData.companionId}`);
  };

  const handleCallCompanion = () => {
    // In a real app, this would initiate a call
    console.log('Calling companion...');
  };

  const handleViewBookings = () => {
    resetBooking();
    router.push('/bookings');
  };

  const handleBackToHome = () => {
    resetBooking();
    router.push('/(app)');
  };

  const handleShareBooking = () => {
    // In a real app, this would open share dialog
    console.log('Sharing booking details...');
  };

  const handleDownloadReceipt = () => {
    // In a real app, this would download/save receipt
    console.log('Downloading receipt...');
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      weekday: 'long',
      month: 'long',
      day: 'numeric',
      year: 'numeric',
    });
  };

  const formatTime = (time: string) => {
    return new Date(`2000-01-01T${time}`).toLocaleTimeString('en-US', {
      hour: 'numeric',
      minute: '2-digit',
      hour12: true,
    });
  };

  const bookingId = createBookingMutation.data?.data?.booking?.id || null;
  const companion = bookingData.companionData;
  const service = bookingData.service;
  const dateTime = bookingData.dateTime;
  const location = bookingData.location;
  const payment = bookingData.payment;

  return (
    <View style={styles.container}>
      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Success Animation */}
        <Animated.View 
          style={[
            styles.successContainer,
            {
              opacity: animationValue,
              transform: [{
                scale: animationValue.interpolate({
                  inputRange: [0, 1],
                  outputRange: [0.8, 1],
                }),
              }],
            },
          ]}
        >
          <View style={styles.successIcon}>
            <CheckCircle size={64} color={designTokens.colors.semantic.success} />
          </View>
          <Text style={styles.successTitle}>Booking Confirmed!</Text>
          <Text style={styles.successSubtitle}>
            Your booking has been successfully submitted
          </Text>
          {bookingId && (
            <View style={styles.bookingIdContainer}>
              <Text style={styles.bookingIdLabel}>Booking ID:</Text>
              <Text style={styles.bookingIdValue}>{bookingId}</Text>
            </View>
          )}
        </Animated.View>

        {/* Companion Contact Info */}
        <Card style={styles.sectionCard} padding={16}>
          <Text style={styles.sectionTitle}>Your Companion</Text>
          
          <View style={styles.companionSection}>
            <ProfileImage
              uri={companion.image}
              size="large"
            />
            <View style={styles.companionInfo}>
              <Text style={styles.companionName}>{companion.name}</Text>
              <View style={styles.companionDetails}>
                <View style={styles.detailRow}>
                  <MapPin size={14} color={designTokens.colors.semantic.textSecondary} />
                  <Text style={styles.detailText}>{companion.location}</Text>
                </View>
                <View style={styles.detailRow}>
                  <Text style={styles.ratingText}>⭐ {companion.rating}/5</Text>
                  {typeof companion.reviews === 'number' && (
                  <Text style={styles.reviewsText}>({companion.reviews} reviews)</Text>
                  )}
                </View>
              </View>
              
              <View style={styles.contactButtons}>
                <TouchableOpacity 
                  style={styles.contactButton}
                  onPress={handleMessageCompanion}
                >
                  <MessageCircle size={18} color={designTokens.colors.semantic.surface} />
                  <Text style={styles.contactButtonText}>Message</Text>
                </TouchableOpacity>
                <TouchableOpacity 
                  style={[styles.contactButton, styles.callButton]}
                  onPress={handleCallCompanion}
                >
                  <Phone size={18} color={designTokens.colors.semantic.primary} />
                  <Text style={[styles.contactButtonText, styles.callButtonText]}>Call</Text>
                </TouchableOpacity>
              </View>
            </View>
          </View>
        </Card>

        {/* Booking Details Summary */}
        <Card style={styles.sectionCard} padding={16}>
          <Text style={styles.sectionTitle}>Booking Details</Text>
          
          <View style={styles.bookingDetails}>
            {service && (
            <View style={styles.detailSection}>
              <Text style={styles.detailSectionTitle}>Service</Text>
                <Text style={styles.detailSectionValue}>{service.name}</Text>
                {service.description && (
                  <Text style={styles.detailSectionSubvalue}>{service.description}</Text>
                )}
            </View>
            )}
            
            {dateTime && (
            <View style={styles.detailSection}>
              <View style={styles.detailRow}>
                <Calendar size={16} color={designTokens.colors.semantic.primary} />
                <Text style={styles.detailSectionTitle}>Date & Time</Text>
              </View>
              <Text style={styles.detailSectionValue}>
                  {formatDate(dateTime.date)}
              </Text>
              <Text style={styles.detailSectionSubvalue}>
                  {formatTime(dateTime.time)} - {formatTime(dateTime.endTime)}
              </Text>
            </View>
            )}
            
            {location && (
            <View style={styles.detailSection}>
              <View style={styles.detailRow}>
                <MapPin size={16} color={designTokens.colors.semantic.primary} />
                <Text style={styles.detailSectionTitle}>Meeting Point</Text>
              </View>
                <Text style={styles.detailSectionValue}>{location.area}</Text>
                <Text style={styles.detailSectionSubvalue}>{location.meetingPoint}</Text>
            </View>
            )}
            
            {payment && (
              <>
            <View style={styles.detailSection}>
              <Text style={styles.detailSectionTitle}>Payment Method</Text>
              <Text style={styles.detailSectionValue}>
                    {payment.method === 'cash' ? 'Cash Payment' :
                     payment.method === 'promptpay' ? 'PromptPay QR' :
                 'Bank Transfer'}
              </Text>
            </View>
            
            <View style={styles.detailSection}>
              <Text style={styles.detailSectionTitle}>Total Amount</Text>
              <Text style={styles.totalAmount}>
                    ฿{payment.totalAmount.toLocaleString()}
              </Text>
            </View>
              </>
            )}
          </View>
        </Card>

        {/* Next Steps */}
        <Card style={styles.sectionCard} padding={16}>
          <Text style={styles.sectionTitle}>What's Next?</Text>
          
          <View style={styles.nextSteps}>
            <View style={styles.stepItem}>
              <View style={styles.stepNumber}>
                <Text style={styles.stepNumberText}>1</Text>
              </View>
              <View style={styles.stepContent}>
                <Text style={styles.stepTitle}>Wait for Confirmation</Text>
                <Text style={styles.stepDescription}>
                  Your companion will confirm the booking within 2 hours
                </Text>
              </View>
            </View>
            
            <View style={styles.stepItem}>
              <View style={styles.stepNumber}>
                <Text style={styles.stepNumberText}>2</Text>
              </View>
              <View style={styles.stepContent}>
                <Text style={styles.stepTitle}>Prepare for Your Experience</Text>
                <Text style={styles.stepDescription}>
                  Bring comfortable shoes, camera, and payment method
                </Text>
              </View>
            </View>
            
            <View style={styles.stepItem}>
              <View style={styles.stepNumber}>
                <Text style={styles.stepNumberText}>3</Text>
              </View>
              <View style={styles.stepContent}>
                <Text style={styles.stepTitle}>Meet at the Location</Text>
                <Text style={styles.stepDescription}>
                  Arrive 5-10 minutes early at the meeting point
                </Text>
              </View>
            </View>
          </View>
        </Card>

        {/* Action Buttons */}
        <View style={styles.actionButtonsContainer}>
          <TouchableOpacity
            style={styles.actionButton}
            onPress={handleShareBooking}
          >
            <View style={styles.actionButtonIcon}>
              <Share size={20} color={designTokens.colors.semantic.primary} />
            </View>
            <Text style={styles.actionButtonText}>Share</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.actionButton}
            onPress={handleDownloadReceipt}
          >
            <View style={styles.actionButtonIcon}>
              <Download size={20} color={designTokens.colors.semantic.primary} />
            </View>
            <Text style={styles.actionButtonText}>Receipt</Text>
          </TouchableOpacity>
        </View>
      </ScrollView>

      {/* Enhanced CTA Footer */}
      <View style={styles.footer}>
        <View style={styles.ctaContainer}>
          <Button
            title="Bookings"
            onPress={handleViewBookings}
            variant="outline"
            style={styles.bookingsButton}
          />
          <Button
            title="Back to Home"
            onPress={handleBackToHome}
            style={styles.homeButton}
            leftIcon={<Home size={18} color={designTokens.colors.semantic.surface} />}
          />
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: designTokens.colors.semantic.background,
  },
  content: {
    flex: 1,
    paddingHorizontal: designTokens.spacing.scale.lg,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: designTokens.spacing.scale.xl,
    gap: designTokens.spacing.scale.lg,
  },
  errorText: {
    ...designTokens.typography.styles.body,
    color: designTokens.colors.semantic.error,
    textAlign: 'center',
  },
  successContainer: {
    alignItems: 'center',
    paddingVertical: designTokens.spacing.scale['2xl'],
  },
  successIcon: {
    marginBottom: designTokens.spacing.scale.xl,
    ...designTokens.shadows.lg,
  },
  successTitle: {
    ...designTokens.typography.styles.heading,
    fontSize: designTokens.typography.sizes.xlarge,
    color: designTokens.colors.semantic.success,
    textAlign: 'center',
    marginBottom: designTokens.spacing.scale.sm,
  },
  successSubtitle: {
    ...designTokens.typography.styles.body,
    color: designTokens.colors.semantic.textSecondary,
    textAlign: 'center',
    marginBottom: designTokens.spacing.scale.xl,
    lineHeight: designTokens.typography.lineHeights.normal * designTokens.typography.sizes.body,
  },
  bookingIdContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: designTokens.spacing.scale.sm,
    paddingHorizontal: designTokens.spacing.scale.lg,
    paddingVertical: designTokens.spacing.scale.md,
    backgroundColor: designTokens.colors.semantic.success + '20',
    borderRadius: designTokens.borderRadius.components.button,
    borderWidth: 1,
    borderColor: designTokens.colors.semantic.success + '40',
    ...designTokens.shadows.sm,
  },
  bookingIdLabel: {
    ...designTokens.typography.styles.caption,
    color: designTokens.colors.semantic.textSecondary,
  },
  bookingIdValue: {
    ...designTokens.typography.styles.caption,
    fontWeight: designTokens.typography.weights.bold,
    color: designTokens.colors.semantic.success,
  },
  sectionCard: {
    marginBottom: designTokens.spacing.scale.lg,
    borderRadius: designTokens.borderRadius.components.card,
    ...designTokens.shadows.md,
  },
  sectionTitle: {
    ...designTokens.typography.styles.subheading,
    color: designTokens.colors.semantic.text,
    marginBottom: designTokens.spacing.scale.lg,
    fontSize: designTokens.typography.sizes.large,
  },
  companionSection: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    gap: designTokens.spacing.scale.lg,
  },
  companionInfo: {
    flex: 1,
  },
  companionName: {
    ...designTokens.typography.styles.subheading,
    color: designTokens.colors.semantic.text,
    marginBottom: designTokens.spacing.scale.xs,
    fontSize: designTokens.typography.sizes.large,
  },
  companionDetails: {
    gap: designTokens.spacing.scale.xs,
    marginBottom: designTokens.spacing.scale.lg,
  },
  detailRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: designTokens.spacing.scale.xs,
    minHeight: 24, // Better touch target
  },
  detailText: {
    ...designTokens.typography.styles.caption,
    color: designTokens.colors.semantic.textSecondary,
  },
  ratingText: {
    ...designTokens.typography.styles.caption,
    color: designTokens.colors.semantic.text,
    fontWeight: designTokens.typography.weights.medium,
  },
  reviewsText: {
    ...designTokens.typography.styles.caption,
    color: designTokens.colors.semantic.textSecondary,
  },
  contactButtons: {
    flexDirection: 'row',
    gap: designTokens.spacing.scale.sm,
  },
  contactButton: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: designTokens.spacing.scale.xs,
    paddingVertical: designTokens.spacing.scale.sm,
    paddingHorizontal: designTokens.spacing.scale.md,
    backgroundColor: designTokens.colors.semantic.primary,
    borderRadius: designTokens.borderRadius.components.button,
    minHeight: 44, // Accessibility touch target
    shadowColor: designTokens.shadows.sm.shadowColor,
    shadowOffset: designTokens.shadows.sm.shadowOffset,
    shadowOpacity: designTokens.shadows.sm.shadowOpacity,
    shadowRadius: designTokens.shadows.sm.shadowRadius,
    elevation: designTokens.shadows.sm.elevation,
  },
  callButton: {
    backgroundColor: 'transparent',
    borderWidth: 1,
    borderColor: designTokens.colors.semantic.primary,
    shadowColor: 'transparent',
    shadowOffset: { width: 0, height: 0 },
    shadowOpacity: 0,
    shadowRadius: 0,
    elevation: 0,
  },
  contactButtonText: {
    ...designTokens.typography.styles.caption,
    fontWeight: designTokens.typography.weights.medium,
    color: designTokens.colors.semantic.surface,
  },
  callButtonText: {
    color: designTokens.colors.semantic.primary,
  },
  bookingDetails: {
    gap: designTokens.spacing.scale.xl,
  },
  detailSection: {
    gap: designTokens.spacing.scale.xs,
    paddingVertical: designTokens.spacing.scale.sm,
  },
  detailSectionTitle: {
    ...designTokens.typography.styles.caption,
    fontWeight: designTokens.typography.weights.semibold,
    color: designTokens.colors.semantic.text,
  },
  detailSectionValue: {
    ...designTokens.typography.styles.body,
    color: designTokens.colors.semantic.text,
    fontWeight: designTokens.typography.weights.medium,
  },
  detailSectionSubvalue: {
    ...designTokens.typography.styles.caption,
    color: designTokens.colors.semantic.textSecondary,
    lineHeight: designTokens.typography.lineHeights.normal * designTokens.typography.sizes.caption,
  },
  totalAmount: {
    ...designTokens.typography.styles.subheading,
    fontWeight: designTokens.typography.weights.bold,
    color: designTokens.colors.semantic.primary,
    fontSize: designTokens.typography.sizes.large,
  },
  nextSteps: {
    gap: designTokens.spacing.scale.xl,
  },
  stepItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    gap: designTokens.spacing.scale.lg,
    paddingVertical: designTokens.spacing.scale.sm,
  },
  stepNumber: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: designTokens.colors.semantic.primary,
    alignItems: 'center',
    justifyContent: 'center',
    ...designTokens.shadows.md,
  },
  stepNumberText: {
    ...designTokens.typography.styles.caption,
    fontWeight: designTokens.typography.weights.bold,
    color: designTokens.colors.semantic.surface,
  },
  stepContent: {
    flex: 1,
  },
  stepTitle: {
    ...designTokens.typography.styles.body,
    fontWeight: designTokens.typography.weights.semibold,
    color: designTokens.colors.semantic.text,
    marginBottom: designTokens.spacing.scale.xs,
  },
  stepDescription: {
    ...designTokens.typography.styles.caption,
    color: designTokens.colors.semantic.textSecondary,
    lineHeight: designTokens.typography.lineHeights.normal * designTokens.typography.sizes.caption,
  },
  actionButtonsContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    gap: designTokens.spacing.scale['2xl'],
    marginVertical: designTokens.spacing.scale.xl,
    paddingHorizontal: designTokens.spacing.scale.lg,
  },
  actionButton: {
    alignItems: 'center',
    gap: designTokens.spacing.scale.sm,
    paddingVertical: designTokens.spacing.scale.lg,
    paddingHorizontal: designTokens.spacing.scale.xl,
    minHeight: 64, // Enhanced touch target
    borderRadius: designTokens.borderRadius.components.card,
    backgroundColor: designTokens.colors.semantic.surface,
    borderWidth: 1,
    borderColor: designTokens.colors.semantic.border,
    shadowColor: designTokens.shadows.sm.shadowColor,
    shadowOffset: designTokens.shadows.sm.shadowOffset,
    shadowOpacity: designTokens.shadows.sm.shadowOpacity,
    shadowRadius: designTokens.shadows.sm.shadowRadius,
    elevation: designTokens.shadows.sm.elevation,
  },
  actionButtonIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: designTokens.colors.semantic.primary + '15',
    alignItems: 'center',
    justifyContent: 'center',
  },
  actionButtonText: {
    ...designTokens.typography.styles.caption,
    fontWeight: designTokens.typography.weights.semibold,
    color: designTokens.colors.semantic.primary,
    fontSize: designTokens.typography.sizes.small,
  },
  footer: {
    padding: designTokens.spacing.scale.lg,
    backgroundColor: designTokens.colors.semantic.surface,
    borderTopWidth: 1,
    borderTopColor: designTokens.colors.semantic.border,
    shadowColor: designTokens.shadows.lg.shadowColor,
    shadowOffset: designTokens.shadows.lg.shadowOffset,
    shadowOpacity: designTokens.shadows.lg.shadowOpacity,
    shadowRadius: designTokens.shadows.lg.shadowRadius,
    elevation: designTokens.shadows.lg.elevation,
  },
  ctaContainer: {
    flexDirection: 'row',
    gap: designTokens.spacing.scale.md,
    alignItems: 'center',
  },
  bookingsButton: {
    flex: 1,
    borderColor: designTokens.colors.semantic.primary,
    borderWidth: 2,
    backgroundColor: 'transparent'
  },
  homeButton: {
    flex: 1.5, // Give more prominence to primary action
    
  },
});
