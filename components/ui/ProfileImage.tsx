import React from 'react';
import { View, StyleSheet, TouchableOpacity } from 'react-native';
import { Image } from 'expo-image';
import { colors } from '@/constants/colors';
import { Camera, Edit } from 'lucide-react-native';

interface ProfileImageProps {
  uri?: string;
  size?: 'small' | 'medium' | 'large' | 'xlarge' | number;
  editable?: boolean;
  onEdit?: () => void;
  online?: boolean;
}

export const ProfileImage: React.FC<ProfileImageProps> = ({
  uri,
  size = 'medium',
  editable = false,
  onEdit,
  online = false,
}) => {
  const sizeMap = {
    small: 40,
    medium: 60,
    large: 80,
    xlarge: 120,
  };

  const imageSize = typeof size === 'number' ? size : sizeMap[size];
  const editButtonSize = imageSize * 0.3;
  
  const defaultImage = 'https://images.unsplash.com/photo-1494790108377-be9c29b29330?q=80&w=1000&auto=format&fit=crop';
  
  return (
    <View style={[styles.container, { width: imageSize, height: imageSize }]}>
      <Image
        source={{ uri: uri || defaultImage }}
        style={[styles.image, { width: imageSize, height: imageSize, borderRadius: imageSize / 2 }]}
        contentFit="cover"
      />
      
      {online && (
        <View
          style={[
            styles.onlineIndicator,
            {
              width: imageSize * 0.25,
              height: imageSize * 0.25,
              borderRadius: (imageSize * 0.25) / 2,
              bottom: imageSize * 0.05,
              right: imageSize * 0.05,
            },
          ]}
        />
      )}
      
      {editable && (
        <TouchableOpacity
          style={[
            styles.editButton,
            {
              width: editButtonSize,
              height: editButtonSize,
              borderRadius: editButtonSize / 2,
              bottom: -editButtonSize * 0.2,
              right: -editButtonSize * 0.2,
            },
          ]}
          onPress={onEdit}
        >
          <Edit size={editButtonSize * 0.5} color={colors.white} />
        </TouchableOpacity>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    position: 'relative',
  },
  image: {
    borderWidth: 3,
    borderColor: colors.white,
  },
  onlineIndicator: {
    position: 'absolute',
    backgroundColor: colors.success,
    borderWidth: 2,
    borderColor: colors.white,
  },
  editButton: {
    position: 'absolute',
    backgroundColor: colors.primary,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: colors.white,
  },
});