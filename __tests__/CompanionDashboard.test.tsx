import React from 'react';
import { render } from '@testing-library/react-native';
import { CompanionDashboard } from '@/components/home/<USER>';

// Mock the dependencies
jest.mock('@/app/api/companion/stats', () => ({
  useSupplierStats: () => ({
    data: { data: { data: null } },
    isLoading: false,
    error: null
  })
}));

jest.mock('@/app/api/bookings', () => ({
  useBookingsQuery: () => ({
    data: { data: { data: [] } },
    isLoading: false,
    error: null,
    refetch: jest.fn()
  })
}));

jest.mock('@/stores/authStore', () => ({
  useAuthStore: () => ({
    isAuthenticated: true,
    user: { id: '1', name: 'Test User' }
  })
}));

jest.mock('expo-router', () => ({
  router: {
    push: jest.fn(),
    replace: jest.fn()
  }
}));

describe('CompanionDashboard', () => {
  it('renders without crashing', () => {
    const { getByText } = render(<CompanionDashboard userName="Test User" />);
    
    // Should render the welcome message or dashboard content
    expect(getByText).toBeDefined();
  });

  it('handles errors gracefully', () => {
    // This test ensures the component has proper error boundaries
    expect(() => {
      render(<CompanionDashboard userName="Test User" />);
    }).not.toThrow();
  });
});
