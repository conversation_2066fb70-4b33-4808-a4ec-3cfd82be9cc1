# Companion App Separation Guide

## Overview
Due to critical Android APK crashes in the main app, this document outlines the complete separation of companion/supplier functionality into a dedicated companion app. The main app will focus on customer functionality only.

## User Type Validation
The current validation logic that needs to be implemented:

```typescript
// Current validation in main app
const isCompanion = useMemo(() => {
  return user?.userType === 'companion' || user?.userType === 'supplier';
}, [user?.userType]);
```

## 🏗️ Core Architecture Components

### 1. Authentication & User Management
**Files to Copy:**
- `stores/auth-store.ts` - Complete auth state management
- `utils/auth.ts` - Auth utilities and token management
- `stores/supplier-store.ts` - Supplier-specific state management

**Key Features:**
- User type validation (`companion` | `supplier`)
- Token-based authentication with SecureStore
- Registration flow for companions
- Demo login functionality
- Profile management

### 2. API Layer
**Files to Copy:**
- `app/api/companion/companion.ts` - Core companion API functions
- `app/api/companion/stats.ts` - Supplier statistics API
- `app/api/bookings/bookings.ts` - Booking management API
- `docs/api-schema.md` - API documentation reference

**Key API Endpoints:**
```typescript
// Companion Management
GET /companions
GET /companions/{id}
POST /companions/{id}/availability
GET /companions/{id}/availability

// Booking Management  
GET /bookings
POST /bookings
PUT /bookings/{id}/status
GET /bookings/{id}

// Statistics & Analytics
GET /supplier/stats
GET /supplier/analytics
GET /supplier/earnings
```

### 3. Navigation Structure
**Companion-Specific Routes:**
```
app/
├── (supplier)/
│   ├── _layout.tsx
│   ├── availability/
│   │   ├── index.tsx
│   │   └── add-slot.tsx
│   ├── services/
│   │   └── index.tsx
│   ├── analytics/
│   │   └── index.tsx
│   ├── profile/
│   │   └── index.tsx
│   └── settings/
│       └── index.tsx
├── supplier/
│   ├── dashboard/
│   │   └── index.tsx
│   └── signup/
│       ├── availability.tsx
│       ├── photos.tsx
│       └── [other signup steps]
└── companion/
    └── [id].tsx (for viewing other companions)
```

## 📱 UI Components

### 1. Dashboard Components
**Primary Component:**
- `components/home/<USER>
  - Today's overview (bookings, rating, weekly stats)
  - Quick actions (Set Availability, Experiences, Analytics, Profile)
  - Recent bookings management
  - Status update functionality

**Supporting Components:**
- `components/supplier/RecentActivityFeed.tsx`
- `components/supplier/EarningsChart.tsx` 
- `components/supplier/PerformanceMetrics.tsx`
- `components/supplier/NotificationCenter.tsx`
- `components/supplier/RequestAnalytics.tsx`

### 2. Booking Management
**Components:**
- Booking list with status management
- Booking confirmation/cancellation
- Customer communication interface
- Booking history and analytics

**Status Types:**
```typescript
type BookingStatus = 'pending' | 'confirmed' | 'completed' | 'cancelled';
```

### 3. Availability Management
**Components:**
- Weekly schedule editor
- Time slot management
- Availability calendar view
- Bulk availability updates

**Key Features:**
- Date/time picker integration
- Recurring availability patterns
- Special availability exceptions
- Real-time availability updates

### 4. Analytics & Performance
**Components:**
- Revenue tracking and charts
- Performance metrics dashboard
- Request analytics
- Response time monitoring
- Customer feedback analysis

**Metrics Tracked:**
- Total requests and acceptance rate
- Response time distribution
- Revenue and booking value trends
- Customer satisfaction ratings

## 🔧 Technical Implementation

### 1. Safe Icon System
**Current Implementation:**
```typescript
// Safe Lucide icon components with error boundaries
const SafeCalendarIcon = () => (
  <ErrorBoundary fallback={<View style={{ width: 24, height: 24, backgroundColor: SAFE_COLORS.primary, borderRadius: 12 }} />}>
    <Calendar size={24} color={SAFE_COLORS.primary} />
  </ErrorBoundary>
);
```

**Required Icons:**
- Calendar (availability)
- Settings (profile/settings)
- TrendingUp (analytics)
- Plus (add new items)
- Clock (time management)
- CheckCircle/XCircle (status indicators)

### 2. Error Handling
**ViewManager Safety:**
- React Native Fabric disabled (`newArchEnabled: false`)
- Error boundaries around all icon components
- ViewManagerSafeWrapper for critical components
- Fallback UI for component failures

### 3. State Management
**Zustand Stores:**
- `useAuthStore` - Authentication state
- `useSupplierStore` - Supplier profile and signup data
- `useBookingStore` - Booking management
- `useToastStore` - User notifications

## 📊 Data Models

### 1. Companion/Supplier Profile
```typescript
interface SupplierProfile {
  id: string;
  name: string;
  displayName: string;
  email: string;
  phone: string;
  profileImage: string;
  gallery: string[];
  location: string;
  bio: string;
  services: SupplierService[];
  availability: SupplierAvailability;
  stats: SupplierStats;
  verified: boolean;
  rating: number;
  reviewCount: number;
}
```

### 2. Booking Management
```typescript
interface Booking {
  id: string;
  customerId: string;
  companionId: string;
  serviceId: string;
  date: string;
  startTime: string;
  endTime: string;
  status: BookingStatus;
  totalAmount: number;
  customerName: string;
  customerImage?: string;
  location: string;
  notes?: string;
}
```

### 3. Availability System
```typescript
interface AvailabilityTimeSlot {
  start: string;
  end: string;
  available: boolean;
  price?: number;
}

interface SupplierAvailability {
  weeklySchedule: Record<string, AvailabilityTimeSlot[]>;
  exceptions: AvailabilityException[];
  timezone: string;
}
```

## 🚀 Quick Actions Implementation

### 1. Set Availability
**Route:** `/(supplier)/availability`
**Features:**
- Weekly schedule management
- Add/edit time slots
- Bulk availability updates
- Calendar integration

### 2. Experiences/Services
**Route:** `/(supplier)/services`
**Features:**
- Service catalog management
- Pricing configuration
- Service descriptions and photos
- Category management

### 3. Analytics
**Route:** `/(supplier)/analytics`
**Features:**
- Performance dashboard
- Revenue tracking
- Request analytics
- Customer feedback

### 4. Profile
**Route:** `/(supplier)/profile`
**Features:**
- Profile information editing
- Photo gallery management
- Verification status
- Account settings

## 🔐 Security Considerations

### 1. Authentication
- JWT token-based authentication
- Secure token storage with expo-secure-store
- Token refresh mechanism
- User type validation on all routes

### 2. Data Protection
- Sensitive data encryption
- Secure API communication
- User permission validation
- Data access logging

## 📱 Platform-Specific Considerations

### 1. Android Optimizations
- React Native Fabric disabled for stability
- ViewManager crash prevention
- Memory optimization for large datasets
- Background task management

### 2. iOS Optimizations
- Native navigation patterns
- iOS-specific UI guidelines
- Push notification handling
- App Store compliance

## 🧪 Testing Strategy

### 1. Component Testing
- Error boundary testing
- Icon component safety
- Navigation flow testing
- State management validation

### 2. Integration Testing
- API endpoint testing
- Authentication flow testing
- Booking workflow testing
- Real-time data synchronization

### 3. Performance Testing
- Memory usage monitoring
- Crash prevention validation
- Load testing for high booking volumes
- Network connectivity handling

## 📋 Migration Checklist

### Phase 1: Core Setup
- [ ] Copy authentication system
- [ ] Set up companion-specific navigation
- [ ] Implement basic dashboard
- [ ] Configure API layer

### Phase 2: Feature Implementation
- [ ] Availability management
- [ ] Booking system
- [ ] Analytics dashboard
- [ ] Profile management

### Phase 3: Testing & Optimization
- [ ] Comprehensive testing
- [ ] Performance optimization
- [ ] Security audit
- [ ] User acceptance testing

### Phase 4: Deployment
- [ ] App store preparation
- [ ] Production deployment
- [ ] User migration strategy
- [ ] Support documentation

## 🔄 Data Synchronization

### 1. Real-time Updates
- WebSocket connections for live booking updates
- Push notifications for new requests
- Automatic data refresh mechanisms
- Offline data caching

### 2. Conflict Resolution
- Optimistic updates with rollback
- Server-side conflict resolution
- User notification for conflicts
- Data consistency validation

## 📦 Dependencies & Packages

### 1. Core Dependencies
```json
{
  "dependencies": {
    "@expo/vector-icons": "^14.1.0",
    "@react-native-async-storage/async-storage": "2.1.2",
    "@react-native-community/datetimepicker": "8.4.1",
    "@react-navigation/native": "^7.1.6",
    "@tanstack/react-query": "^5.81.2",
    "axios": "^1.10.0",
    "expo": "^53.0.4",
    "expo-linear-gradient": "~14.1.5",
    "expo-router": "~5.0.3",
    "expo-secure-store": "^14.2.3",
    "lucide-react-native": "^0.475.0",
    "react": "19.0.0",
    "react-native": "0.79.1",
    "zustand": "^5.0.5"
  }
}
```

### 2. Configuration Files
**app.json:**
```json
{
  "expo": {
    "name": "Tirak Companion",
    "slug": "tirak-companion-app",
    "newArchEnabled": false,
    "android": {
      "package": "com.tirak.companion",
      "enableProguardInReleaseBuilds": false,
      "enableHermes": true
    }
  }
}
```

## 🎨 Design System

### 1. Color Tokens
```typescript
const SAFE_COLORS = {
  primary: '#007AFF',
  accent: '#FF9500',
  success: '#34C759',
  error: '#FF3B30',
  surface: '#FFFFFF',
  textSecondary: '#666666'
};
```

### 2. Component Styling
- RadialGradient backgrounds for visual appeal
- Card-based layout system
- Consistent spacing and typography
- Platform-specific adaptations

## 🔄 Real-time Features

### 1. Live Booking Updates
```typescript
// WebSocket connection for real-time updates
const useBookingUpdates = () => {
  useEffect(() => {
    const ws = new WebSocket(`${WS_URL}/bookings`);
    ws.onmessage = (event) => {
      const update = JSON.parse(event.data);
      // Update booking state
    };
    return () => ws.close();
  }, []);
};
```

### 2. Push Notifications
- New booking requests
- Booking status changes
- Customer messages
- Payment confirmations

## 🛠️ Development Tools

### 1. Testing Setup
```javascript
// jest.config.js
module.exports = {
  testEnvironment: 'node',
  moduleNameMapping: {
    '^@/(.*)$': '<rootDir>/$1',
  },
};
```

### 2. Metro Configuration
```javascript
// metro.config.js - Optimized for ViewManager stability
const config = getDefaultConfig(__dirname);
config.resolver.platforms = ['ios', 'android', 'native', 'web'];
config.transformer.minifierConfig = {
  keep_fnames: true,
  mangle: { keep_fnames: true }
};
```

## 📱 App Store Metadata

### 1. App Information
- **Name:** Tirak Companion - Cultural Guide Dashboard
- **Description:** Professional dashboard for cultural guides and companions in Thailand
- **Keywords:** cultural guide, companion, booking management, Thailand tourism
- **Category:** Business/Travel

### 2. Screenshots Required
- Dashboard overview
- Booking management
- Availability calendar
- Analytics dashboard
- Profile management

## 🚨 Critical Fixes Applied

### 1. Android Crash Prevention
- **React Native Fabric disabled** (`newArchEnabled: false`)
- **ViewManagerSafeWrapper** for critical components
- **Error boundaries** around all Lucide icons
- **Safe fallback rendering** for component failures

### 2. Memory Management
- Optimized image loading and caching
- Efficient state management with Zustand
- Proper cleanup of event listeners
- Background task optimization

This separation will create a focused, stable companion app that addresses the Android crash issues while providing a superior experience for companion users.
